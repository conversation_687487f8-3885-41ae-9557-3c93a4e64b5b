<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.1</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.jinghang.jyh</groupId>
    <artifactId>loan</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>loan</name>
    <description>捷易花</description>
    <packaging>pom</packaging>

    <modules>
        <module>loan-cash-flow-api</module>
        <module>loan-cash-capital-api</module>
        <module>sing-service</module>
        <module>third-pay</module>
        <module>jh-loan-cash-capital</module>
        <module>jh-loan-cash-flow</module>
        <module>jh-loan-cash-manage</module>
    </modules>


</project>
