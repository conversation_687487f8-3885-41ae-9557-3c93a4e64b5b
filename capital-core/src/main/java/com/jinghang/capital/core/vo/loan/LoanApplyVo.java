package com.jinghang.capital.core.vo.loan;

import com.jinghang.capital.api.dto.EntrustedPay;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.LoanPurpose;
import com.jinghang.capital.core.vo.FileInfoVo;
import com.jinghang.capital.core.vo.credit.BankCardInfoVo;
import java.math.BigDecimal;
import java.util.List;

public class LoanApplyVo {
    /**
     * 外部放款id
     */
    private String sysId;
    /**
     * 外部授信id
     */
    private String sysCreditId;
    /**
     * 授信id
     */
    private String creditId;
    /**
     * 绑卡id
     */
    private String cardId;
    /**
     * 还款协议号
     */
    private String repayAgreementNo;
    /**
     * 放款金额
     */
    private BigDecimal loanAmt;
    /**
     * 期数
     */
    private Integer periods;
    /**
     * 资产端放款申请合同编号
     */
    private String loanApplyContractNo;
    /**
     * 资产端委托担保合同编号
     */
    private String guaranteeContractNo;
    /**
     * 对客利率
     */
    private BigDecimal customRate;
    /**
     * 借款用途 todo 假如这里的蚂蚁要求的用途有10中，而原来的是7种，是从源头改，还是在末端归类一下
     */
    private LoanPurpose loanPurpose;

    /**
     * 融担公司
     */
    private GuaranteeCompany guaranteeCompany;

    /**
     * 辅助模式
     */
    private String assistMode;
    /**
     * 协议信息
     */
    private List<FileInfoVo> fileInfoVoList;
    /**
     * 银行卡信息
     */
    private BankCardInfoVo bankCardInfo;


    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 是否受托支付
     */
    private EntrustedPay entrustedPay;

    /**
     * 关联的项目唯一编码
     */
    private String projectCode;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }


    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }


    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getSysCreditId() {
        return sysCreditId;
    }

    public void setSysCreditId(String sysCreditId) {
        this.sysCreditId = sysCreditId;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getRepayAgreementNo() {
        return repayAgreementNo;
    }

    public void setRepayAgreementNo(String repayAgreementNo) {
        this.repayAgreementNo = repayAgreementNo;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public String getLoanApplyContractNo() {
        return loanApplyContractNo;
    }

    public void setLoanApplyContractNo(String loanApplyContractNo) {
        this.loanApplyContractNo = loanApplyContractNo;
    }

    public String getGuaranteeContractNo() {
        return guaranteeContractNo;
    }

    public void setGuaranteeContractNo(String guaranteeContractNo) {
        this.guaranteeContractNo = guaranteeContractNo;
    }

    public BigDecimal getCustomRate() {
        return customRate;
    }

    public void setCustomRate(BigDecimal customRate) {
        this.customRate = customRate;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getAssistMode() {
        return assistMode;
    }

    public void setAssistMode(String assistMode) {
        this.assistMode = assistMode;
    }

    public List<FileInfoVo> getFileInfoVoList() {
        return fileInfoVoList;
    }

    public void setFileInfoVoList(List<FileInfoVo> fileInfoVoList) {
        this.fileInfoVoList = fileInfoVoList;
    }

    public BankCardInfoVo getBankCardInfo() {
        return bankCardInfo;
    }

    public void setBankCardInfo(BankCardInfoVo bankCardInfo) {
        this.bankCardInfo = bankCardInfo;
    }

    public EntrustedPay getEntrustedPay() {
        return entrustedPay;
    }

    public void setEntrustedPay(EntrustedPay entrustedPay) {
        this.entrustedPay = entrustedPay;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }
}
