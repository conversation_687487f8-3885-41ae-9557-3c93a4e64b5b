package com.maguo.loan.after.common.controller.cs;

import com.maguo.loan.after.common.common.ResultMessage;
import com.maguo.loan.after.common.entity.dto.cs.ReductionDto;
import com.maguo.loan.after.common.service.cs.ReductionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/collect")
public class ReductionController {

    private static final Logger logger = LoggerFactory.getLogger(ReductionController.class);

    @Autowired
    private ReductionService reductionService;

    // 减免
    @RequestMapping(value = "/reduction", method = RequestMethod.POST)
    public ResultMessage reduction(@RequestBody ReductionDto reductionDto) {
        logger.info("入参：{}", reductionDto);
        ResultMessage result = reductionService.reduction(reductionDto);
        logger.info("返回：{}", result);
        if (result.getStatus() == 200) {
            return ResultMessage.success();
        } else {
            return ResultMessage.error(result.getStatus(), result.getMessage());
        }
    }

    // 减免校验
    @RequestMapping(value = "/check", method = RequestMethod.POST)
    public ResultMessage reductionCheck(@RequestBody ReductionDto reductionDto) {
        logger.info("入参：{}", reductionDto);
        ResultMessage result = reductionService.reductionCheck(reductionDto);
        logger.info("返回：{}", result);
        if (result.getStatus() == 200) {
            return ResultMessage.success();
        } else {
            return ResultMessage.error(result.getStatus(), result.getMessage());
        }
    }
}
