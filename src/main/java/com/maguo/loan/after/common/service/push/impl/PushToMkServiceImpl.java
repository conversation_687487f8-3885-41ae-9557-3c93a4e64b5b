package com.maguo.loan.after.common.service.push.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.maguo.loan.after.common.dto.pushmk.*;
import com.maguo.loan.after.common.entity.*;
import com.maguo.loan.after.common.enums.AccessType;
import com.maguo.loan.after.common.enums.RepayState;
import com.maguo.loan.after.common.enums.RightsBizType;
import com.maguo.loan.after.common.enums.WhetherState;
import com.maguo.loan.after.common.exception.ShmBusinessException;
import com.maguo.loan.after.common.repository.*;
import com.maguo.loan.after.common.repository.common.CommonCodeConfigRepository;
import com.maguo.loan.after.common.service.push.AbstractPushToMkService;
import com.maguo.loan.after.common.util.AgeUtil;
import com.maguo.loan.after.common.util.PushMkConvertUtil;
import com.maguo.loan.after.common.util.SerialNumGenerator;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class PushToMkServiceImpl extends AbstractPushToMkService {

    private static final Logger log = LoggerFactory.getLogger(PushToMkServiceImpl.class);


    @Value("${shm.push.submit.url}")
    private String submitUrl;
    @Value("${shm.push.submit.des}")
    private String des;


    @Resource
    private RepayPlanRepository repayPlanRepository;

    @Resource
    private UserInfoRepository userInfoRepository;

    @Resource
    private UserOcrRepository userOcrRepository;

    @Resource
    private LoanRepository loanRepository;

    @Resource
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private UserBankCardRepository userBankCardRepository;

    @Resource
    private UserContactInfoRepository userContactInfoRepository;

    @Resource
    private CommonCodeConfigRepository commonCodeConfigRepository;


    @Override
    public void execute(String loanId) {
        super.execute(loanId);
    }


    @Override
    public CustomerInfoPushDto getCustomerInfoPushDto(String loanId) {
        return this.initPushData(loanId);
    }

    /**
     *  初始化数据
     * @return
     */
    private CustomerInfoPushDto initPushData(String loanId){
        log.info("组装报文---开始：{}", loanId);
        CustomerInfoPushDto customerInfoPushDto = new CustomerInfoPushDto();
        List<RepayPlan> repayPlans = repayPlanRepository.findByLoanIdOrderByPeriod(loanId);
        if (CollectionUtils.isEmpty(repayPlans)) {
            log.info("该笔数据未查询到还款计划：{}", loanId);
            //throw new ShmBusinessException("该笔数据未查询到还款计划:" + loanId);
        }
        customerInfoPushDto.setLoanslipNumber(loanId);
        List<RepayRefInfo> repayRefList = repayPlans.stream()
                .map(repayPlan -> {
                    RepayRefInfo info = new RepayRefInfo();
                    info.setPeriodNum(ObjectUtils.isEmpty(repayPlan.getPeriod()) ? null : repayPlan.getPeriod().toString());
                    // info.setStartDate()无起息日
                    info.setSettleDate(ObjectUtils.isEmpty(repayPlan.getPlanRepayDate()) ? null : repayPlan.getPlanRepayDate().toString());// 应还日期 (yyyy-MM-dd) 传计划还款日
                    info.setTradeDate(ObjectUtils.isEmpty(repayPlan.getActRepayTime()) ? null : repayPlan.getActRepayTime().toLocalDate().toString());// 还款日期 (yyyy-MM-dd) 传实还时间
                    info.setNeedAmount(repayPlan.getAmount());
                    info.setNeedCorpus(repayPlan.getPrincipalAmt());
                    info.setNeedAccrual(repayPlan.getInterestAmt());
                    info.setNeedPunish(repayPlan.getPenaltyAmt());
                    // info.setNeedRecvcomp();//无 应还复息
                    info.setAlreadyAmount(repayPlan.getActAmount());
                    info.setAlreadyCorpus(repayPlan.getActPrincipalAmt());
                    info.setAlreadyAccrual(repayPlan.getActInterestAmt());
                    info.setAlreadyPunish(repayPlan.getActPenaltyAmt());
                    // info.setAlreadyRecvcomp();//无 已还复息
                    // info.setCorpusStatus();// 无 本金状态
                    info.setNeedFee(repayPlan.getConsultFee());
                    info.setAlreadyFee(ObjectUtils.isEmpty(repayPlan.getActConsultFee()) ? BigDecimal.ZERO : repayPlan.getActConsultFee());
                    info.setLoanNumber(loanId);
                    return info;
                })
                .collect(Collectors.toList());
        RepaymentPlan repaymentPlan = new RepaymentPlan();
        repaymentPlan.setTotalPeriod(repayPlans.size());
        repaymentPlan.setRepayRefList(repayRefList);

        ArrayList<RepaymentPlan> repaymentPlans = new ArrayList<>();
        repaymentPlans.add(repaymentPlan);
        customerInfoPushDto.setRepaymentPlan(repaymentPlans);


        customerInfoPushDto.setTransactionID(SerialNumGenerator.generateSerial());
        String userId = repayPlans.get(0).getUserId();
        if (ObjectUtils.isEmpty(userId)) {
            log.info("还款计划数据中客户id缺失：{}", loanId);
            //throw new ShmBusinessException("还款计划数据中客户id缺失:" + loanId);
        }
        UserOcr userOcr = userOcrRepository.findByUserId(userId);
        if (!ObjectUtils.isEmpty(userOcr)) {
            customerInfoPushDto.setAge(AgeUtil.calculateAge(userOcr.getCertNo()));
            customerInfoPushDto.setIDCardNumber(userOcr.getCertNo());
            // 民族信息转换
            customerInfoPushDto.setEthnicity(this.getCodeDesc(userOcr.getNation()));
            customerInfoPushDto.setSex(PushMkConvertUtil.genderToSex(userOcr.getGender()));
            customerInfoPushDto.setIDCardType("1");
            customerInfoPushDto.setIDCardAddress(userOcr.getCertAddress());
            customerInfoPushDto.setHouseholdAddress(userOcr.getCertAddress());
            customerInfoPushDto.setIDCardEndDate(ObjectUtils.isEmpty(userOcr.getCertValidEnd()) ? null : userOcr.getCertValidEnd().toString());
            UserInfo userInfo = userInfoRepository.findByCertNo(userOcr.getCertNo());
            if (!ObjectUtils.isEmpty(userInfo)) {
                customerInfoPushDto.setEducationalBackground(PushMkConvertUtil.educationToBackgroundCode(userInfo.getEducation()));
                customerInfoPushDto.setEmail(userInfo.getEmail());
                customerInfoPushDto.setMarriage(PushMkConvertUtil.marriageTo(userInfo.getMarriage()));
                customerInfoPushDto.setName(userInfo.getName());
                customerInfoPushDto.setPhoneNumber(userInfo.getMobile());
                customerInfoPushDto.setResidenceAddress(userInfo.getLivingAddress());
            }
        }
        List<Loan> allByUserId = loanRepository.findAllByUserId(userId);
        if (!ObjectUtils.isEmpty(allByUserId)) {
            List<LoanApplyLog> loanApplyLogList = allByUserId.stream()
                    .map(order -> {
                        LoanApplyLog loanApplyLog = new LoanApplyLog();
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        loanApplyLog.setTime(ObjectUtils.isEmpty(order.getCreatedTime()) ? null : order.getCreatedTime().format(formatter));
                        loanApplyLog.setChannel(ObjectUtils.isEmpty(order.getBankChannel()) ? null : order.getBankChannel().getName());
                        loanApplyLog.setProd(ObjectUtils.isEmpty(order.getFlowChannel()) ? null : order.getFlowChannel().getDesc());
                        loanApplyLog.setApprovalResult(ObjectUtils.isEmpty(order.getLoanState()) ? null : order.getLoanState().toString());
                        loanApplyLog.setRefusalReason(order.getFailReason());
                        loanApplyLog.setAmount(order.getAmount());
                        loanApplyLog.setPurpose(ObjectUtils.isEmpty(order.getLoanPurpose()) ? null : order.getLoanPurpose().getDesc());
                        loanApplyLog.setCycle(ObjectUtils.isEmpty(order.getPeriods()) ? null : order.getPeriods().toString());
                        loanApplyLog.setLoanNumber(order.getId());
                        return loanApplyLog;
                    })
                    .collect(Collectors.toList());
            customerInfoPushDto.setLoanApplyLog(loanApplyLogList);
        }
        //-------------
        List<EmergencyContactInfo> contact = this.getContact(userId);
        if(!CollectionUtils.isEmpty(contact)) {
            customerInfoPushDto.setEmergencyContact(contact);
        }
        List<ServiceListInfo> serviceList = this.getServiceList(allByUserId);
        if(!CollectionUtils.isEmpty(serviceList)) {
            customerInfoPushDto.setServiceList(serviceList);
        }
        List<RepaymentDetails> repaymentDetails = this.getRepaymentDetails(repayPlans);
        if(!CollectionUtils.isEmpty(repaymentDetails)) {
            customerInfoPushDto.setRepaymentDetail(repaymentDetails);
        }
        log.info("组装报文---完成：{}", loanId);
        return customerInfoPushDto;
    }

    @Override
    protected String getBody(String loanId) {
        CustomerInfoPushDto customerInfoPushDto = this.initPushData(loanId);
        SerializeConfig config = new SerializeConfig();
        config.propertyNamingStrategy = PropertyNamingStrategy.PascalCase;
        return JSON.toJSONString(customerInfoPushDto, config);
    }

    /**
     *  民族字段转换
     * @param code
     * @return
     */
    private String getCodeDesc(String code){
        String desc = commonCodeConfigRepository.queryCodeDesc(code);
        if(!ObjectUtils.isEmpty(desc)){
            return desc;
        }
        return code;
    }


    /**
     *
     *  获取还款明细
     * @param repayPlans
     * @return
     */
    private List<RepaymentDetails> getRepaymentDetails(List<RepayPlan> repayPlans){
        List<RepaymentDetails> repaymentDetails = new CopyOnWriteArrayList<>();
        if(!CollectionUtils.isEmpty(repayPlans)){
            int asInt = repayPlans.size();
            long repaidCount = repayPlans.stream().filter(f -> f.getCustRepayState().equals(RepayState.REPAID)).count();
            long normalCount = repayPlans.stream().filter(f -> f.getCustRepayState().equals(RepayState.NORMAL)).count();
            repayPlans.parallelStream().forEach(x->{
                RepaymentDetails details = new RepaymentDetails();
                OverdueRefInfo RepayRefList = new OverdueRefInfo();
                // 总期数
                details.setTotalPeriod(asInt);
                // 已还
                details.setTotalAlready(Integer.parseInt(String.valueOf(repaidCount)));
                // 剩余
                details.setTotalRemaining(Integer.parseInt(String.valueOf(normalCount)));
                //int count = getPeriod(x.getLoanId(), x.getPeriod());
                Order order = this.getOrder(repayPlans.get(0).getLoanId());
                if(!ObjectUtils.isEmpty(order)) {
                    RepayRefList.setLoanInterestRate(order.getIrrRate());
                }
                UserBankCard userBankCard = this.getBankCard(repayPlans.get(0).getLoanId());
                if(!ObjectUtils.isEmpty(userBankCard)) {
                    RepayRefList.setAccount(ObjectUtils.isEmpty(userBankCard.getCardNo()) ? null : userBankCard.getCardNo());
                    RepayRefList.setOpenBank(ObjectUtils.isEmpty(userBankCard.getBankName()) ? null : userBankCard.getBankName());
                }
                if(x.getCustRepayState().equals(RepayState.REPAID)) {
                    RepayRefList.setIsSettle("是");
                }
                if(x.getCustRepayState().equals(RepayState.NORMAL)){
                    RepayRefList.setIsSettle("否");
                }
                RepayRefList.setOverdueDays(this.getOverdueDays(x.getLoanId(), x.getPeriod()));
                RepayRefList.setOverdueMoney(x.getPrincipalAmt());
                RepayRefList.setOverdueInterest(x.getInterestAmt());
                RepayRefList.setOverduePunishment(x.getPenaltyAmt());
                RepayRefList.setOverdueHandlingFee(x.getConsultFee());
                RepayRefList.setPeriodNum(x.getPeriod());
                details.setRepayRefList(RepayRefList);
                repaymentDetails.add(details);
            });
        }
        return repaymentDetails;
    }

    /**
     *  获取 逾期天数
     * @param loanId
     * @param period
     * @return
     */
    private int getOverdueDays(String loanId,int period){
        return repayPlanRepository.findOverdueDays(loanId, period);
    }

    /**
     *  获取联系人信息
     * @param userId
     * @return
     */
    private List<EmergencyContactInfo> getContact(String userId){
        List<EmergencyContactInfo> contactInfoList = new ArrayList<>();
        List<UserContactInfo> userContactInfos = userContactInfoRepository.queryByUserIdOrderByUpdatedTimeDesc(userId);
        if(!CollectionUtils.isEmpty(userContactInfos)){
            userContactInfos.forEach(userContactInfo -> {
                EmergencyContactInfo contactInfo = new EmergencyContactInfo();
                contactInfo.setPhone(userContactInfo.getPhone());
                contactInfo.setName(userContactInfo.getName());
                contactInfo.setRelationship(userContactInfo.getRelation().getDesc());
                contactInfoList.add(contactInfo);
            });
        }
        return contactInfoList;
    }

    /**
     *  获取产品信息
     * @param allByUserId
     * @return
     */
    private  List<ServiceListInfo> getServiceList(List<Loan> allByUserId){
        List<ServiceListInfo> infos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(allByUserId)){
            allByUserId.forEach(x->{
                ServiceListInfo serviceListInfo = new ServiceListInfo();
                serviceListInfo.setAccountStatus(0);
                serviceListInfo.setPordType(0);
                // 流量渠道
                String appName = x.getFlowChannel().getDesc();
                // 资金渠道
                String bankName = x.getBankChannel().getName();
                // 融担公司
                String company = x.getGuaranteeCompany().getShortName();
                // 渠道标签
                String applyChannel = this.getCodeDesc(x.getApplyChannel());
                // 是否权益
                String ioncludingEquity = RightsBizType.GUARANTEE.getDesc();
                if(!ObjectUtils.isEmpty(x.getIsIncludingEquity())){
                    WhetherState state = x.getIsIncludingEquity();
                    if(state.name().equals("Y")){
                        ioncludingEquity = RightsBizType.RIGHTS.getDesc();
                    }
                }
                String accessType = appName + "-" + bankName + "-" + company+ "-"+applyChannel +"-"+ioncludingEquity;
                //serviceListInfo.setAccessType(ObjectUtils.isEmpty(x.getApplyChannel())? null: Integer.valueOf(Objects.requireNonNull(AccessType.getAccessTypeDescByCode(x.getApplyChannel()))));
                serviceListInfo.setAccessType(accessType);
                if(!ObjectUtils.isEmpty(serviceListInfo.getAccessType())){
                    infos.add(serviceListInfo);
                }
            });
        }
       return infos;
    }

    /**
     *  通过字典name 获取 value
     * @param code
     * @return
     */
    private String getApplyChannelName(String code){
        return commonCodeConfigRepository.queryCodeDesc(code);
    }


    /**
     *  获取订单信息
     * @return
     */
    private UserBankCard getBankCard(String loanId) {
        Optional<Loan> loan = loanRepository.findById(loanId);
        if (loan.isPresent()) {
            Optional<UserBankCard> bankCard = userBankCardRepository.findById(loan.get().getRepayCardId());
            if (bankCard.isPresent()) {
                return bankCard.get();
            }
        }
        return null;
    }

    /**
     *  获取订单信息
     * @return
     */
    private Order getOrder(String loanId) {
        Optional<Loan> loan = loanRepository.findById(loanId);
        if (loan.isPresent()) {
            Optional<Order> order = orderRepository.findById(loan.get().getOrderId());
            if (order.isPresent()) {
                return order.get();
            }
        }
        return null;
    }


    /**
     *  获取已还其次
     * @return
     */
    private List<CustomRepayRecord> getRepayRecord(String loanId,int period) {
        return customRepayRecordRepository.findAllByLoanIdAndPeriod(loanId, period);
    }


    @Override
    protected String getUrl() {
        return submitUrl;
    }


    @Override
    protected String msgEncrypt(String msg) {
        //log.info("请求明文为：{}", msg);
        try {
            // 1. 计算MD5并处理密钥
            String md5Key = md5(des).toUpperCase().substring(0, 8);
            byte[] keyBytes = md5Key.getBytes(StandardCharsets.UTF_8);

            // 2. 初始化DES加密器
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "DES");
            IvParameterSpec iv = new IvParameterSpec(keyBytes);
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

            // 3. 执行加密
            byte[] textBytes = msg.getBytes(StandardCharsets.UTF_8);
            byte[] encrypted = cipher.doFinal(textBytes);

            // 4. 转换为十六进制字符串
            return bytesToHex(encrypted);
        } catch (Exception e) {
            log.error("DES Encryption failed", e);
            throw new ShmBusinessException("DES Encryption failed");
        }
    }

    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(input.getBytes(StandardCharsets.UTF_8));

            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("MD5 calculation failed", e);
            throw new ShmBusinessException("MD5 calculation failed");
        }
    }


    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b)); // 大写 ,与C#的{0:X2}一致
        }
        return sb.toString();
    }

}
