package com.maguo.loan.after.common.job.jh;

import com.alibaba.fastjson2.JSON;
import com.maguo.loan.after.common.dto.pushmk.CustomerInfoPushDto;
import com.maguo.loan.after.common.entity.BatchRecordLog;
import com.maguo.loan.after.common.entity.ShmPushFailFlow;
import com.maguo.loan.after.common.enums.BatchType;
import com.maguo.loan.after.common.enums.PushType;
import com.maguo.loan.after.common.job.AbstractJobHandler;
import com.maguo.loan.after.common.job.JobParam;
import com.maguo.loan.after.common.repository.BatchRecordLogRepository;
import com.maguo.loan.after.common.repository.RepayPlanRepository;
import com.maguo.loan.after.common.repository.ShmPushFailFlowRepository;
import com.maguo.loan.after.common.service.FileService;
import com.maguo.loan.after.common.service.MqService;
import com.maguo.loan.after.common.service.push.impl.PushToMkThread;
import com.maguo.loan.after.common.util.CsvGenerator;
import com.maguo.loan.after.common.util.DateUtil;
import com.maguo.loan.after.common.util.rsa.CSVEncryptor;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.io.FileInputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Component
@JobHandler("PushCusInfoToMkJhKsJob")
public class PushCusInfoToMkJhKsJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PushCusInfoToMkJhKsJob.class);

    private static final int BATCH_SIZE = 1000;

    @Value("${shm.push.job.full}")
    private String isFull;

    @Value("${shm.push.job.type}")
    private Integer pushType;

    @Value("${shm.push.job.filePath}")
    private String pushFilePath;

    @Value("${shm.push.job.fileOutPath}")
    private String pushFileOutPath;

    @Value("${shm.push.job.OssPath}")
    private String pushOssPath;

    @Value("${aliyun.ossks.bucket}")
    private String pushOssBucket;

    @Resource
    private PushToMkThread pushToMkThread;

    @Resource
    private RepayPlanRepository repayPlanRepository;

    @Resource
    private MqService mqService;

    @Resource
    private ShmPushFailFlowRepository shmPushFailFlowRepository;

    @Resource
    private BatchRecordLogRepository recordLogRepository;

    @Resource
    private FileService fileService;


    @Override
    public void doJob(JobParam jobParam) {
        logger.info("定时任务批量扫描数据推送至上海脉开始");
        try {
            logger.info("PushCusInfoToMkJhKsJob------流量开关：{}------",isFull);
            if (isFull.equals("Y")) {
                logger.info("PushCusInfoToMkJhKsJob------全量推送------");
                //pushAllInfo();
                pushAllInfoPage();
            } else {
                logger.info("PushCusInfoToMkJhKsJob------增量推送------");
                if(ObjectUtils.isEmpty(jobParam)){
                    LocalDateTime startTime = null;
                    LocalDateTime endTime = LocalDateTime.now().withMinute(0).withSecond(0).withNano(0);
                    int hour = endTime.getHour();
                    if(10 == hour){
                        startTime = endTime.minusHours(11).withMinute(0).withSecond(0).withNano(0);
                    }else{
                        startTime = endTime.minusHours(1).withMinute(0).withSecond(0).withNano(0);
                    }
                    pushIncrement(startTime,endTime);
                }else{
                    if(!ObjectUtils.isEmpty(jobParam.getStartDateTime()) && !ObjectUtils.isEmpty(jobParam.getEndDateTime())){
                        logger.info("数据补录开始----startTime:{},endTime:{}",DateUtil.formatLocalDateTime(jobParam.getStartDateTime()),DateUtil.formatLocalDateTime(jobParam.getEndDateTime()));
                        this.repairData(jobParam.getStartDateTime(),jobParam.getEndDateTime());
                        if(!ObjectUtils.isEmpty(jobParam.getChannel())){
                            logger.info("还款数据补录开始----startTime:{},endTime:{}",DateUtil.formatLocalDateTime(jobParam.getStartDateTime()),DateUtil.formatLocalDateTime(jobParam.getEndDateTime()));
                            this.repairRepayData(jobParam.getStartDateTime(),jobParam.getEndDateTime());
                        }
                    }
                }

            }
            logger.info("定时任务批量扫描数据推送至上海脉完成");
        } catch (Exception e) {
            logger.error("定时任务批量扫描数据推送至上海脉异常:", e);
        }
    }

    private void reTryPush() {
        logger.info("PushCusInfoToMkJhKsJob------处理失败数据重推------");
        Long lastId = null;
        String lastLoanId = null;
        int totalProcessed = 0;
        try {
            List<ShmPushFailFlow> loanIds;
            while (true) {
                // 批量查询loanId
                 loanIds = shmPushFailFlowRepository.findLoanId(lastId,
                    BATCH_SIZE
                );
                if (loanIds.isEmpty()) {
                    break;
                }
                List<Long> ids = loanIds.stream()
                    .map(ShmPushFailFlow::getId)
                    .collect(Collectors.toList());

                int i = shmPushFailFlowRepository.updateIsRepushByIds(ids);
                logger.info("将该批次数据：{} 条，更新为已重推", i);

                List<String> loanIdList = loanIds.stream()
                    .map(ShmPushFailFlow::getLoanId)
                    .collect(Collectors.toList());

                loanIdList.forEach(x->{
                    pushToMkThread.shmPush(x);
                });
                // 更新lastLoanId
                lastId = loanIds.get(loanIds.size() - 1).getId();

                totalProcessed += loanIds.size();

                lastLoanId = loanIdList.get(loanIds.size() - 1);

                loanIds.clear();

                logger.info("失败记录查询已处理: {}, 当前id: {}", totalProcessed, lastId);
            }
            logger.info("重试日志: {}, 当前id: {}", totalProcessed, lastLoanId);
            if(Objects.nonNull(lastLoanId)){
                this.recordLogs(totalProcessed,lastLoanId,"0");
            }
        } catch (Exception e) {
            this.recordLogs(totalProcessed,lastLoanId,"1");
        }
    }


    private void pushIncrement(LocalDateTime startDateTime,LocalDateTime endDateTime) {
        logger.info("增量数据开始----startTime:{},endTime:{}",DateUtil.formatLocalDateTime(startDateTime),DateUtil.formatLocalDateTime(endDateTime));
        String lastLoanId = null;
        int totalProcessed = 0;
        logger.info("PushCusInfoToMkJhKsJob-----推送方式:{}------",pushType);
        if(PushType.INTERFACES.getSort().equals(pushType)){
            try {
                List<String> loanIds;
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime startOfDay = now.toLocalDate().atStartOfDay();
                while (true){
                    boolean firstTriggerFlag = Boolean.FALSE;
                    // 首次触发
                    BatchRecordLog log = getDailyFirstData(now);
                    logger.info("本次增量查询数据为：{}", JSON.toJSONString(log));
                    if(Objects.isNull(log)){
                        firstTriggerFlag = Boolean.TRUE;
                    }
                    if(firstTriggerFlag){
                        logger.info("本次首次增量查询数据为：{}", DateUtil.formatLocalDateTime(startOfDay));
                        loanIds = repayPlanRepository.findDistinctLoanIdByDate(startOfDay,BATCH_SIZE);
                        if (loanIds.isEmpty()) {
                            break;
                        }
                        loanIds.forEach(x->{
                            pushToMkThread.shmPush(x);
                        });
                        this.recordLogs(loanIds.size(),loanIds.get(0),"0");
                        break;
                    }else{
                        if(Objects.isNull(lastLoanId)){
                            lastLoanId = log.getBatchLastflag();
                        }
                        //logger.info("本次增量查询数据最大id：{}", lastLoanId);
                        loanIds = repayPlanRepository.findDistinctLoanIdPage(totalProcessed,BATCH_SIZE,startDateTime,endDateTime);
                        logger.info("本次增量查询条数为：{}", loanIds.size());
                        if (loanIds.isEmpty()) {
                            break;
                        }
                        loanIds.forEach(x->{
                            pushToMkThread.shmPush(x);
                        });
                        // 更新lastLoanId
                        lastLoanId = loanIds.get(loanIds.size() - 1);
                        totalProcessed += loanIds.size();
                        loanIds.clear();
                        logger.info("增量查询已处理: {}, 当前id: {}", totalProcessed, lastLoanId);
                    }
                }
                if(Objects.nonNull(lastLoanId)){
                    this.recordLogs(totalProcessed,lastLoanId,"0");
                }
            } catch (Exception e) {
                this.recordLogs(totalProcessed,lastLoanId,"1");
            }
            reTryPush();
        }
        if(PushType.FILE.getSort().equals(pushType)){
            try {
                List<String> resultList = new ArrayList<>();
                List<String> loanIds;
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime startOfDay = now.toLocalDate().atStartOfDay();
                while (true){
                    boolean firstTriggerFlag = Boolean.FALSE;
                    // 首次触发
                    BatchRecordLog log = getDailyFirstData(now);
                    if(Objects.isNull(log)){
                        firstTriggerFlag = Boolean.TRUE;
                    }
                    if(firstTriggerFlag){
                        loanIds = repayPlanRepository.findDistinctLoanIdByDate(startOfDay,BATCH_SIZE);
                        if (loanIds.isEmpty()) {
                            break;
                        }
                        resultList.addAll(loanIds);
                        this.recordLogs(loanIds.size(),loanIds.get(0),"0");
                        break;
                    }else{
                        if(Objects.isNull(lastLoanId)){
                            lastLoanId = log.getBatchLastflag();
                        }
                        loanIds = repayPlanRepository.findDistinctLoanIdPage(totalProcessed,BATCH_SIZE,startDateTime,endDateTime);
                        if (loanIds.isEmpty()) {
                            break;
                        }
                        resultList.addAll(loanIds);
                        // 更新lastLoanId
                        lastLoanId = loanIds.get(loanIds.size() - 1);
                        totalProcessed += loanIds.size();
                        loanIds.clear();
                        logger.info("增量查询已处理: {}, 当前id: {}", totalProcessed, lastLoanId);
                    }
                }
                List<CustomerInfoPushDto> collect = resultList.parallelStream().map(x -> {
                    return pushToMkThread.shmPushFile(x);
                }).collect(Collectors.toList());

                if(Objects.nonNull(lastLoanId)){
                    this.recordLogs(totalProcessed,lastLoanId,"0");
                }
                logger.info("开始上传文件,文件路径：{}", pushFilePath);
                String filePath = CsvGenerator.generateCsvWithPipes(collect,pushFilePath);
                createFile(filePath,collect.size());
            } catch (Exception e) {
                this.recordLogs(totalProcessed,lastLoanId,"1");
            }
        }
    }


    /**
     *  还款数据补录
     * @param startTime
     * @param endTime
     */
    private void repairRepayData(LocalDateTime startTime,LocalDateTime endTime){
        int offset = 0;
        try {
            List<String> loanIds;
            while (true){
                loanIds = repayPlanRepository.findRepairDistinctLoanIds(startTime,endTime);
                if (loanIds.isEmpty()) {
                    break;
                }
                loanIds.forEach(x->{
                    pushToMkThread.shmPush(x);
                });
                offset += loanIds.size();
                loanIds.clear();
            }
            logger.info("还款补录数据查询已处理: {}", offset);
        } catch (Exception e) {
            this.recordLogs(offset,null,"1");
        }
    }


    /**
     *  数据补录
     * @param startTime
     * @param endTime
     */
    private void repairData(LocalDateTime startTime,LocalDateTime endTime){
        int offset = 0;
        try {
            List<String> loanIds;
            while (true){
                loanIds = repayPlanRepository.findDistinctLoanIdPage(offset,BATCH_SIZE,startTime,endTime);
                if (loanIds.isEmpty()) {
                    break;
                }
                loanIds.forEach(x->{
                    pushToMkThread.shmPush(x);
                });
                offset += loanIds.size();
                loanIds.clear();
            }
            logger.info("补录数据查询已处理: {}", offset);
        } catch (Exception e) {
            this.recordLogs(offset,null,"1");
        }
    }

    /**
     *  创建 文件 并上传
     * @param filePath
     * @param count
     */
    private void createFile(String filePath,int count){
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String fileDate = now.format(formatter);
        String ossPath = pushOssPath.replace("yyyyMMdd",fileDate);
        DateTimeFormatter hh = DateTimeFormatter.ofPattern("HH");
        String fileDateHH = now.format(hh);
        String path = ossPath + "custloandetail_"+fileDate+"_"+fileDateHH+".csv";
        try {
            CSVEncryptor.encryptor(filePath,pushFileOutPath,"public.key");
            FileInputStream in = new FileInputStream(pushFileOutPath);
            // 上传加密文件
            fileService.uploadOss(pushOssBucket,path,in);
            logger.info("上传加密文件成功,文件路径：{}",path);
            // 生成ok 文件
            String pathOk = ossPath + "custloandetail_"+fileDate+"_"+fileDateHH+".ok";
            String filePathOk = CsvGenerator.generateCsvWithPipesOK(count,pushFilePath);
            FileInputStream inOK = new FileInputStream(filePathOk);
            // 上传ok 文件
            fileService.uploadOss(pushOssBucket,pathOk,inOK);
            logger.info("上传ok文件成功,文件路径：{}", pathOk);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void recordLogs(int totalProcessed,String loanId,String status){
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String batchDate = now.format(formatter);
        BatchRecordLog log = new BatchRecordLog();
        log.setBatchCode(BatchType.CUSTOMER_COMPLAINT.getCode());
        log.setBatchName(BatchType.CUSTOMER_COMPLAINT.getDesc());
        log.setBatchDate(batchDate);
        log.setBatchNum(getBatchNum(batchDate));
        log.setBatchLinecounts(totalProcessed);
        log.setBatchLastflag(loanId);
        log.setBatchStatus(status);
        recordLogRepository.save(log);
    }

    private Integer getBatchNum(String batchDate){
        Integer i = recordLogRepository.countByBatchNum(BatchType.CUSTOMER_COMPLAINT.getCode(),batchDate);
        if(Objects.isNull(i)){
            return 0;
        }
        return i+1;
    }

    private void pushAllInfoPage() {
        String lastLoanId = null;
        int offset = 0;
        try {
            List<String> loanIds;
            while (true) {
                // 批量查询loanId
                loanIds = repayPlanRepository.findDistinctPageLoanIds(
                        offset,
                        BATCH_SIZE
                );
                logger.info("本次全量查询条数为：{}", loanIds.size());
                if (loanIds.isEmpty()) {
                    break;
                }
                loanIds.forEach(x->{
                    pushToMkThread.shmPush(x);
                });
                // 更新lastLoanId
                lastLoanId = loanIds.get(loanIds.size() - 1);
                offset += loanIds.size();
                logger.info("全量查询已处理: {}, 当前id: {}", offset, lastLoanId);
            }
            logger.info("全量日志: {}, 当前id: {}", offset, lastLoanId);
            if(Objects.nonNull(lastLoanId)){
                this.recordLogs(offset,lastLoanId,"0");
            }
        } catch (Exception e) {
            this.recordLogs(offset,lastLoanId,"1");
        }
    }


    private void pushAllInfo() {
        String lastLoanId = null;
        int totalProcessed = 0;
        try {
            List<String> loanIds;
            while (true) {
                // 批量查询loanId
                 loanIds = repayPlanRepository.findDistinctLoanIds(
                    lastLoanId,
                    BATCH_SIZE
                );
                logger.info("本次全量查询条数为：{}", loanIds.size());
                if (loanIds.isEmpty()) {
                    break;
                }
                // 发送mq
                //mqService.submitShmPush(loanIds);
                loanIds.forEach(x->{
                    pushToMkThread.shmPush(x);
                });
                // 更新lastLoanId
                lastLoanId = loanIds.get(loanIds.size() - 1);
                totalProcessed += loanIds.size();
                loanIds.clear();
                logger.info("全量查询已处理: {}, 当前id: {}", totalProcessed, lastLoanId);
            }
            logger.info("全量日志: {}, 当前id: {}", totalProcessed, lastLoanId);
            if(Objects.nonNull(lastLoanId)){
                this.recordLogs(totalProcessed,lastLoanId,"0");
            }
        } catch (Exception e) {
            this.recordLogs(totalProcessed,lastLoanId,"1");
        }
    }

    /**
     *  日间首次触发数据
     */
    private BatchRecordLog getDailyFirstData(LocalDateTime now) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String batchDate = now.format(formatter);
        return recordLogRepository.findFirstData(BatchType.CUSTOMER_COMPLAINT.getCode(), batchDate,1);
    }


}
