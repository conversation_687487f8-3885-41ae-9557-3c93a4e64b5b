package com.maguo.loan.after.common.enums;

/**
 * 渠道来源
 *
 */
public enum AccessType {

    PAI_PAI1("p001", "拍拍贷"),
    PAI_PAI2("p002", "拍拍贷"),

    K<PERSON><PERSON>3("p003", "KOO"),
    K<PERSON><PERSON><PERSON>("p004", "KOO"),

    XIAOHUI_PAY5("p005", "小辉付"),
    XIAOHUI_PAY6("p006", "小辉付"),

    XINHE_YUAN("01", "绿信"),
    XINHE_YUANQ("01q", "绿信")
    ;


    private final String code;
    private final String desc;

    AccessType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }


    public static AccessType getAccessType(String name) {
        for (AccessType applyChannel : AccessType.values()) {
            if (applyChannel.getDesc().equals(name)) {
                return applyChannel;
            }
        }
        return null;
    }

    public static AccessType getAccessTypeByCode(String code) {
        for (AccessType applyChannel : AccessType.values()) {
            if (applyChannel.getCode().equals(code)) {
                return applyChannel;
            }
        }
        return null;
    }

    public static String getAccessTypeDescByCode(String code) {
        for (AccessType applyChannel : AccessType.values()) {
            if (applyChannel.getCode().equals(code)) {
                return applyChannel.getDesc();
            }
        }
        return null;
    }

}
