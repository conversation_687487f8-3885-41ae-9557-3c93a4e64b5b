package com.maguo.loan.after.common.repository;

import com.maguo.loan.after.common.entity.ShmPushFailFlow;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ShmPushFailFlowRepository extends JpaRepository<ShmPushFailFlow, String> {

    @Query(value = """
    SELECT *  FROM shm_push_fail_flow
    WHERE
     (is_repush IS NULL OR is_repush = '')
    AND (id > :id OR :id IS NULL)
    ORDER BY id
    LIMIT :batchSize
    """, nativeQuery = true)
    List<ShmPushFailFlow> findLoanId(Long id, int batchSize);


    @Query(value = """
    SELECT *  FROM shm_push_fail_flow WHERE is_repush IS NULL ORDER BY id LIMIT :num, :batchSize
    """, nativeQuery = true)
    List<ShmPushFailFlow> findLoanIds(int num, int batchSize);



    @Modifying
    @Transactional
    @Query(value = "UPDATE shm_push_fail_flow SET is_repush = 'Y' WHERE id IN (:ids)", nativeQuery = true)
    int updateIsRepushByIds(List<Long> ids);
}
