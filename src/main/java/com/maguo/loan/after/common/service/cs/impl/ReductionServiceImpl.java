package com.maguo.loan.after.common.service.cs.impl;

import com.alibaba.fastjson2.JSONObject;
import com.maguo.loan.after.common.common.ResultMessage;
import com.maguo.loan.after.common.entity.dto.cs.ReductionDto;
import com.maguo.loan.after.common.entity.po.cs.ReductionApplyRecord;
import com.maguo.loan.after.common.repository.cs.ReductionRepository;
import com.maguo.loan.after.common.service.cs.PushService;
import com.maguo.loan.after.common.service.cs.ReductionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class ReductionServiceImpl implements ReductionService {

    private static final Logger logger = LoggerFactory.getLogger(ReductionServiceImpl.class);

    @Value("${loan.cash.flow.url}")
    private String url;

    @Autowired
    private ReductionRepository reductionRepository;

    @Autowired
    private PushService pushService;

    @Override
    public ResultMessage reduction(ReductionDto reductionDto) {
        // 插入减免记录
        // 查询外部订单号
        String loanReqNo = reductionRepository.queryOuterOrderId(reductionDto.getOrderId());
        ReductionApplyRecord reductionApplyRecord = new ReductionApplyRecord();
        BeanUtils.copyProperties(reductionDto, reductionApplyRecord);
        reductionApplyRecord.setReduction_status(0);
        reductionApplyRecord.setLoanReqNo(loanReqNo);
        reductionRepository.save(reductionApplyRecord);
        // 调用核心减免接口
        // 请求报文组装
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("loanReqNo", loanReqNo);
        jsonObject.put("applyNo", reductionDto.getApplyNo());
        jsonObject.put("applyTerm", reductionDto.getApplyTerm());
        jsonObject.put("applyReductAmount", reductionDto.getApplyReductAmount());
        String jsonString = jsonObject.toJSONString();
        logger.info("减免请求报文：{}", jsonString);
        // 请求url
        String pushUrl = url + "/collect/reduction";
        // 发送请求
        String message = null;
        try {
            message = pushService.sendPost(jsonString, pushUrl);
            logger.info("返回结果：{}", message);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 结果处理
        String code = JSONObject.parseObject(message).getString("code");
        String msg = JSONObject.parseObject(message).getString("msg");
        if ("000000".equals(code)) {
            String ppStatus = JSONObject.parseObject(message).getJSONObject("data").getString("status");
            String ppMsg = JSONObject.parseObject(message).getJSONObject("data").getString("msg");
            if ("00".equals(ppStatus)) {
                // 修改减免状态为成功
                reductionRepository.update(reductionDto.getApplyNo());
                return ResultMessage.success();
            } else {
                return ResultMessage.error(Integer.parseInt(ppStatus), ppMsg);
            }
        } else {
            return ResultMessage.error(Integer.parseInt(code), msg);
        }
    }

    @Override
    public ResultMessage reductionCheck(ReductionDto reductionDto) {
        // 查询外部订单号
        String loanReqNo = reductionRepository.queryOuterOrderId(reductionDto.getOrderId());
        // 调用核心减免校验接口
        // 请求报文组装
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("loanReqNo", loanReqNo);
        jsonObject.put("applyTerm", reductionDto.getApplyTerm());
        jsonObject.put("applyReductAmount", reductionDto.getApplyReductAmount());
        String jsonString = jsonObject.toJSONString();
        logger.info("减免校验请求报文：{}", jsonString);
        // 请求url
        String pushUrl = url + "/collect/api/reduction/check";
        // 发送请求
        String message = null;
        try {
            message = pushService.sendPost(jsonString, pushUrl);
            logger.info("返回结果：{}", message);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 结果处理
        String status = JSONObject.parseObject(message).getString("status");
        String msg = JSONObject.parseObject(message).getString("msg");
        if ("true".equals(status)) {
            return ResultMessage.success();
        } else {
            return ResultMessage.error(500, msg);
        }
    }
}
