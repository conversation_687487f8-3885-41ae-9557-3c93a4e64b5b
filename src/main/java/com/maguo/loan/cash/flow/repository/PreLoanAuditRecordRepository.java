package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.PreLoanAuditRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;


public interface PreLoanAuditRecordRepository extends JpaRepository<PreLoanAuditRecord, String> {

    Optional<PreLoanAuditRecord> findByOrderId(String loanId);

    @Query("select count(*) from PreLoanAuditRecord pre "
        + "where pre.approveResult = 'AUDITING' "
        + "and pre.createdTime > curdate()"
        + "and timestampdiff(minute ,pre.createdTime, now())>5")
    Integer countPreLOanTimeout();
}

