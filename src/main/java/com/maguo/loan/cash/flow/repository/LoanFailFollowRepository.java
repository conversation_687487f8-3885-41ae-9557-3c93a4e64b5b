package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.LoanFailFollow;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface LoanFailFollowRepository extends JpaRepository<LoanFailFollow, String> {

    List<LoanFailFollow> findAllByIdIn(List<String> loanIds);

    Optional<LoanFailFollow> findByLoanId(String loanId);
}
