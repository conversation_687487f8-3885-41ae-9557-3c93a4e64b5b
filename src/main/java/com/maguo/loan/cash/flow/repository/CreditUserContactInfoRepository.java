package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.CreditUserContactInfo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface CreditUserContactInfoRepository extends JpaRepository<CreditUserContactInfo, String> {
    List<CreditUserContactInfo> findByCreditUserId(String creditUserId);
}
