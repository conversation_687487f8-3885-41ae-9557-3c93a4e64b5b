package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.CollisionMarketingSuccessRecord;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
public interface CollisionMarketingSuccessRecordRepository extends JpaRepository<CollisionMarketingSuccessRecord, String> {

    @Query("select c from CollisionMarketingSuccessRecord c where c.phone=?1 and c.applyChannel=?2 ORDER BY c.createdTime DESC LIMIT 1")
    Optional<CollisionMarketingSuccessRecord> findByMobile(String phoneMd5, ApplyChannel applyChannel);

    CollisionMarketingSuccessRecord findTopByPhoneAndApplyChannelAndCreatedTimeBetweenOrderByCreatedTimeDesc(String phoneMd5, ApplyChannel applyChannel,
                                                                                                             LocalDateTime startTime, LocalDateTime endTime);
}
