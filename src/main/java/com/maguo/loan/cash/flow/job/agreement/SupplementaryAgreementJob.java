package com.maguo.loan.cash.flow.job.agreement;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.config.LvXinNewSFTPConfig;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.job.ppd.PPDSignJob;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

@Component
@JobHandler("supplementaryAgreementJob")
public class SupplementaryAgreementJob extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(LoanAgreementJob.class);
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    LoanAgreementJob job;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private UserFileRepository userFileRepository;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private FileService fileService;
    @Autowired
    private LvXinConfig lvXinConfig;
    @Autowired
    PPDSignJob ppdSignJob;
    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;
    @Override
    public void doJob(JobParam jobParam) {
        List<Loan> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(jobParam.getLoanIds())) {
            logger.info("supplementaryAgreementJob loanIds size:{}", jobParam.getLoanIds().size());
            //根据借据id数组查询
            list = loanRepository.findAllById(jobParam.getLoanIds());
        }
        if (CollectionUtils.isEmpty(list)) {
            logger.info("supplementaryAgreementJob 补协议");
            return;
        }
        for (Loan loan : list) {
            //复借场景需查询客户首借时传输给资金方的协议（综合授权书、数字证书使用授权协议 ），复制一份到复借对应的授信编号的路径下
            Order order = orderRepository.findOrderById(loan.getOrderId());
            //获取流量系统文件
            // 这里根据riskId查询AgreementSignatureRecord表流量签署的协议
            List<AgreementSignatureRecord> agreementList = agreementSignatureRecordRepository.findByOrderNoAndSignState(order.getOuterOrderId(), ProcessState.SUCCEED.name());
            if (agreementList != null && agreementList.size() > 0) {
                for (AgreementSignatureRecord record : agreementList) {
                    if (FlowChannel.LVXIN.equals(loan.getFlowChannel())) {
                        job.uploadFlowAgreementFileToSftp(loan, record.getCommonOssUrl(), record.getFileType(), order);
                    }
                    if (FlowChannel.PPCJDL.equals(loan.getFlowChannel())) {
                        ppdSignJob.uploadPaiPaiFlowAgreementFileToSftp(loan, record.getCommonOssUrl(), record.getFileType());
                    }
                }
            }
            job.handleReloanProtocolFiles(loan,order);
            // 获取资金系统签署完成与资方返回的协议
            List<UserFile> userFileList = userFileRepository.findByUserIdAndLoanNo(loan.getUserId(), loan.getId());
            //上传
            logger.info("获取资金系统签署完成与资方返回的协议:{}", JsonUtil.toJsonString(userFileList));
            if (userFileList != null && userFileList.size() > 0) {
                for (UserFile userFile : userFileList) {
                    if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.CREDIT_SETTLE_VOUCHER_FILE)) {
                        continue;
                    }
                    if (FlowChannel.LVXIN.equals(loan.getFlowChannel())) {
                        lvxinAgreementFileToSftp(loan, userFile, order);
                    }
                    if (FlowChannel.PPCJDL.equals(loan.getFlowChannel())) {
                        ppdSignJob.uploadPaiPaiUserFileToSftp(loan, userFile);
                    }
                }
            }
        }
    }

    public String lvxinAgreementFileToSftp(Loan loan, UserFile userFile, Order order) {
        AtomicReference<String> result = new AtomicReference<>("");
        fileService.getOssFile(userFile.getOssBucket(), userFile.getOssKey(), inputStream -> {
            try {
                /**
                 * 《综合授权书(通用)》 synthesisAuthorization_06.pdf
                 * 《数字证书使用授权协议》digitalCertificateAuthorizationLetter_07.pdf
                 * 《个人客户扣款授权书》 entrustedDeductionLetter_08.pdf
                 * 《借款合同》loanContract_09.pdf
                 * 《资方征信授权书》synthesisAuthorization_06.pdf
                 * 《仲裁协议》arbitrationAgreement_10.pdf
                 */
                String fileName;
                if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.LOAN_CONTRACT)) {
                    fileName = "loanContract_09.pdf";
                } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.ENTRUSTED_DEDUCTION_LETTER)) {
                    fileName = "entrustedDeductionLetter_08.pdf";
                } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER)) {
                    fileName = "digitalCertificateAuthorizationLetter_07.pdf";
                } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.SYNTHESIS_AUTHORIZATION)) {
                    fileName = "synthesisAuthorization_06.pdf";
                } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT)) {
                    // 新增资方征信授权书
                    fileName = "synthesisAuthorization_06.pdf";
                } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.ARBITRATION_AGREEMENT)) {
                    // 新增资方仲裁协议
                    fileName = "arbitrationAgreement_10.pdf";
                } else {
                    fileName = userFile.getFileType().name();
                }
                // 根据资方渠道区分不同的sftp账号
                if (loan.getBankChannel() == BankChannel.CYBK) {
                    String remoteDir = lvXinConfig.getAgreementSftpPath() + formatLocalDateTime(loan.getLoanTime()) + "/" + order.getOuterOrderId();
                    result.set(remoteDir);
                    sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, remoteDir);
                    logger.info("上传文件lvxin——长银SFTP, fileName:{}, remotePath:{}", fileName, remoteDir);
                }
            } catch (Exception e) {
                logger.error("协议文件上传绿信sftp失败:", e);
                throw new RuntimeException(e);
            }
        });
        return result.get();
    }

    private static String formatLocalDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return dateTime.format(formatter);
    }
}
