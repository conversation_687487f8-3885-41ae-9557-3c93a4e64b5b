package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.BindCardRelation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface BindCardRelationRepository extends JpaRepository<BindCardRelation, String> {
    BindCardRelation findByUserId(String userId);

    Optional<BindCardRelation> findByBindCardApplyId(String applyId);
}
