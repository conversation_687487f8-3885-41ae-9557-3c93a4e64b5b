package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.WithholdFlow;
import com.maguo.loan.cash.flow.enums.ChargeBizType;
import com.maguo.loan.cash.flow.enums.Payee;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface WithholdFlowRepository extends JpaRepository<WithholdFlow, String> {

    //WithholdFlow findByRepayRecordIdAndBizType(String loanId,String period);

   // List<WithholdFlow> findByLoanIdPeriod(String loanId,Integer period);


    Optional<WithholdFlow> findByRepayRecordIdAndBizTypeAndPayState(String repayRecordId, ChargeBizType bizType, ProcessState payState);

    @Query("select count(*) from WithholdFlow w "
            + "where w.payState ='PROCESSING' "
            + "and w.createdTime > curdate() "
            + "and timestampdiff(minute ,w.createdTime, now())>15")
    Integer batchWithholdTimeout();

    boolean existsByLoanIdAndPeriodAndBizTypeInAndPayStateIn(String loanId, Integer period, List<ChargeBizType> bizType, List<ProcessState> payState);

    List<WithholdFlow> findByCreatedTimeBetweenAndPayStateIn(LocalDateTime beginTime, LocalDateTime endTime, ProcessState... payStates);

    @Query("SELECT w  FROM WithholdFlow w"
            + " WHERE w.bizType = ?1 "
            + " AND w.payState = ?2 "
            + " AND w.payee = ?3 "
            + " AND w.payTime >= ?4 "
            + " AND w.payTime <= ?5 "
            + " AND w.commonWithholdType = ?6 "
            + " and NOT EXISTS (SELECT 1 FROM BankRepayRecord b WHERE b.loanId = w.loanId and b.period = w.period and b.state = 'SUCCEED' )")
    List<WithholdFlow> findNeedRepushRecords(ChargeBizType bizType,
                                             ProcessState payState,
                                             Payee payee,
                                             LocalDateTime startTime,
                                             LocalDateTime endTime,
                                             String commonWithholdType);

    /**
     * 查询代偿后还款/代偿前平台扣款，未通知fin-core或通知后未成功的扣款数量
     */
    @Query(value = "SELECT count(*) FROM WithholdFlow w "
            + "WHERE w.bizType = 'FINANCE' AND w.payState = 'SUCCEED' AND w.payTime >= ?1 AND w.payTime < ?2 "
            + "AND NOT EXISTS ( SELECT 1 FROM BankRepayRecord b WHERE b.sourceRecordId = w.repayRecordId AND b.state = 'SUCCEED' )")
    Integer countByDeductNotifyCoreFail(LocalDateTime beginTime, LocalDateTime endTime);

    WithholdFlow findByRepayRecordId(String repayRecordId);
}
