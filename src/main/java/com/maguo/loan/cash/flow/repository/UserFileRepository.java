package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.WhetherState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;


public interface UserFileRepository extends JpaRepository<UserFile, String> {

    List<UserFile> findByUserIdAndLoanStage(String userId, LoanStage stage);

    UserFile findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(String userId, LoanStage stage, FileType fileType);

    UserFile findTopByUserIdAndFileTypeOrderByUpdatedTimeDesc(String userId, FileType fileType);

    List<UserFile> findByUserIdAndFileTypeIn(String userId, List<FileType> fileTypes);

    List<UserFile> findByLoanStageAndUserIdInAndFileTypeIn(LoanStage loanStage, List<String> userIds, List<FileType> fileTypes);

    boolean existsByUserIdAndFileType(String userId, FileType fileType);

    List<UserFile> findByFileTypeInAndIdIn(List<String> fileTypeList, List<String> signApplyIds);

    List<UserFile> findAllByUserIdIn(List<String> userIds);

    List<UserFile> findByUserId(String userId);


    List<UserFile> findByLoanNoAndFileType(String loanId, FileType fileType);

    // 添加根据loan_no&sign_final为Y协议列表
    List<UserFile> findByLoanNo(String loanNo);


    @Query(value = "SELECT uf.* " +
        "FROM user_file uf " +
        "INNER JOIN agreement_signature_record asr ON uf.id = asr.id " +
        "INNER JOIN ( " +
        "    SELECT risk_id FROM pre_order WHERE order_no = :orderNo " +
        "    UNION " +
        "    SELECT risk_id FROM `order` WHERE outer_order_id = :orderNo " +
        ") source_risks ON asr.risk_id = source_risks.risk_id " +
        "WHERE asr.sign_state = 'SUCCEED' " +
        "AND uf.file_type = :fileType " +
        "ORDER BY uf.created_time DESC " +
        "LIMIT 1",
        nativeQuery = true)
    Optional<UserFile> findLatestUserFileByOrderNoAndFileType(
        @Param("orderNo") String orderNo,
        @Param("fileType") String fileType);

    UserFile findTopByLoanNoAndFileTypeOrderByCreatedTimeDesc(String loanId, FileType fileType);
    // 添加根据user_id&loan_no协议列表
    List<UserFile> findByUserIdAndLoanNo(String userId, String loanNo);

    List<UserFile> findByUserIdAndLoanNoAndFileTypeInOrderByUpdatedTimeDesc(String userId, String loanNo, List<FileType> fileTypes);
}
