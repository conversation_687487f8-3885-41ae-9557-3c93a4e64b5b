package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.BfCashWithdrawalRecord;
import org.springframework.data.jpa.repository.JpaRepository;

public interface BfCashWithdrawalRecordRepository extends JpaRepository<BfCashWithdrawalRecord, String> {
    BfCashWithdrawalRecord findByServerTransId(String serverTransId);

    BfCashWithdrawalRecord findByTransSerialNo(String transSerialNo);

    // 查询进度最新的记录
    BfCashWithdrawalRecord findFirstByServerTransIdOrderByStateDesc(String serverTransId);



}
