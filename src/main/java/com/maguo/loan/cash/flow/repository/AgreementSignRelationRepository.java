package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.LoanStage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface AgreementSignRelationRepository extends JpaRepository<AgreementSignRelation, String> {
    List<AgreementSignRelation> findByRelatedId(String relatedId);

    Optional<AgreementSignRelation> findBySignApplyId(String signApplyId);

    @Query("select uf from AgreementSignRelation asr inner join UserFile uf on uf.id = asr.signApplyId "
        + "where asr.relatedId in ?1 and asr.loanStage = ?2 and uf.fileType in ?3")
    List<UserFile> findAllByUserFileBySignApplyId(List<String> relatedId, LoanStage loanStage, List<FileType> fileType);

    @Query("select uf from AgreementSignRelation asr inner join UserFile uf on uf.id = asr.signApplyId "
        + "where asr.relatedId = ?1 and asr.loanStage = ?2")
    List<UserFile> getUserFiles(String relatedId, LoanStage loanStage);

    @Query("select uf from AgreementSignRelation asr inner join UserFile uf on uf.id = asr.signApplyId "
        + "where asr.relatedId = ?1")
    List<UserFile> queryUserFiles(String relatedId);
}
