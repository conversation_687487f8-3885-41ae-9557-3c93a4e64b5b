package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.RevolvingStrategyConfig;
import com.maguo.loan.cash.flow.enums.WhetherState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * RevolvingStrategyConfig 持久层接口
 */
public interface RevolvingStrategyConfigRepository extends JpaRepository<RevolvingStrategyConfig, String> {

    List<RevolvingStrategyConfig> findAllByStrategyState(WhetherState whetherState);
}
