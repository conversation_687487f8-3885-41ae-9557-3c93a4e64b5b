package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.PpdRepayApplyRecord;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR> gale
 * @Description
 * @Date 2024/2/19 16:26
 */
public interface PpdRepayApplyRecordRepository extends JpaRepository<PpdRepayApplyRecord, String> {

    Optional<PpdRepayApplyRecord> findByOutRepayId(String outRepayId);
    boolean existsByOutRepayId(String outRepayId);

    PpdRepayApplyRecord findByOutLoanIdAndLoanIdAndOutRepayId(String loanGid, String partnerOrderNo, String repaymentGid);
}
