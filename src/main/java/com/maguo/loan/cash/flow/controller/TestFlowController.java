package com.maguo.loan.cash.flow.controller;

import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ApprovalRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "test/")
public class TestFlowController {

    @RequestMapping(value = "flow")
    public @ResponseBody LvxinResponse testFlow(@RequestBody ApprovalRequest request) {
        return LvxinResponse.success();
    }

}
