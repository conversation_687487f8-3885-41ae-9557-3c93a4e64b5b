package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.AppStationMessage;
import com.maguo.loan.cash.flow.enums.MsgStatusEnum;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
public interface AppStationMessageRepository extends JpaRepository<AppStationMessage, String> {

    List<AppStationMessage> findByUserId(String userId, Pageable pageable);

    int countByUserId(String userId);

    AppStationMessage findBySourceBizIdAndStatus(String sourceBizId, MsgStatusEnum status);

    Optional<AppStationMessage> findBySourceBizId(String sourceBizId);
}
