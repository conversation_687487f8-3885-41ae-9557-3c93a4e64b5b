package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.PaymentChannelConfig;
import com.maguo.loan.cash.flow.enums.PaymentChannel;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024-12-30
 */
public interface PaymentChannelConfigRepository extends JpaRepository<PaymentChannelConfig, String> {

    Optional<PaymentChannelConfig> findByPaymentChannelCode(PaymentChannel paymentChannelCode);
}
