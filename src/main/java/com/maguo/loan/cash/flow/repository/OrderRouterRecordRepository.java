package com.maguo.loan.cash.flow.repository;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.OrderRouterRecord;
import com.maguo.loan.cash.flow.enums.RouteState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/12/20
 */
public interface OrderRouterRecordRepository extends JpaRepository<OrderRouterRecord, String> {
    List<OrderRouterRecord> findByOrderId(String orderId);
    Optional<OrderRouterRecord> findByOrderIdAndBankChannel(String orderId, BankChannel bankChannel);
    Optional<OrderRouterRecord> findByOrderIdAndRouteConfigId(String orderId, String routeConfigId);

    List<OrderRouterRecord> findByOrderIdAndRouteStateIn(String orderId, RouteState... routeStates);
    List<OrderRouterRecord> findByOrderIdAndRouteStateOrderByPriority(String orderId, RouteState state);
}
