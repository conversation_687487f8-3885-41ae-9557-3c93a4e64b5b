package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface BindCardRecordRepository extends JpaRepository<BindCardRecord, String> {
    BindCardRecord findByConfirmOrderNo(String confirmOrderNo);

    List<BindCardRecord> findByUserId(String userId);

    BindCardRecord findTopByUserIdAndBoundSideOrderByCreatedTimeDesc(String userId, BoundSide boundSide);

    List<BindCardRecord> findByCertNoAndBankCardNoAndState(String certNo, String bankCardNo, ProcessState state);

    BindCardRecord findFirstByCertNoAndBankCardNoAndBoundSideOrderByCreatedTimeDesc(String certNo, String bankCardNo, BoundSide boundSide);

    List<BindCardRecord> findByUserIdAndBankCardNo(String userId, String bankCardNo);

    BindCardRecord findFirstByCertNoAndBankCardNoAndStateOrderByCreatedTimeDesc(String certNo, String bankCardNo, ProcessState state);
}
