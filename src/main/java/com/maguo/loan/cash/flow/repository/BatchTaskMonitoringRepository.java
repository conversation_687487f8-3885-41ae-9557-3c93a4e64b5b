package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.BatchTaskMonitoring;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;

public interface BatchTaskMonitoringRepository extends JpaRepository<BatchTaskMonitoring, String> {
    @Query("select c from BatchTaskMonitoring c where  c.taskStartTime=?1 and  c.loanId is null ")
    List<BatchTaskMonitoring> findBatchTaskData( LocalDate executionTime );

    @Query("select c from BatchTaskMonitoring c where  c.taskStartTime=?1 and c.taskHandler=?2 and  c.loanId is null ")
    List<BatchTaskMonitoring> findBatchTaskHandler(  LocalDate executionTime,String taskHandler );
}
