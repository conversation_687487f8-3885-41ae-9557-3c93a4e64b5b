package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.CollisionRecord;
import com.maguo.loan.cash.flow.enums.AuditState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface CollisionRecordRepository extends JpaRepository<CollisionRecord, String> {

    int countByCreatedTimeIsBetween(LocalDateTime startTime, LocalDateTime endTime);

    int countByCreatedTimeIsBetweenAndState(LocalDateTime startTime, LocalDateTime endTime, AuditState state);

    CollisionRecord findTopByPhoneOrderByCreatedTimeDesc(String phone);
    CollisionRecord findTopByCertNoOrderByCreatedTimeDesc(String phone);
}
