package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteePlan;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDate;
import java.util.List;

/**
 * @Classname RepayExtraGuaranteePlanRepository
 * @Description 融担费还款计划
 * @Date 2023/10/23 18:36
 * @Created by gale
 */
public interface RepayExtraGuaranteePlanRepository extends JpaRepository<RepayExtraGuaranteePlan, String> {
    RepayExtraGuaranteePlan findByLoanIdAndPeriod(String loanId, Integer period);
    Boolean existsByLoanIdAndPeriod(String loanId, Integer period);

    RepayExtraGuaranteePlan findByLoanIdAndPeriodAndPlanState(String loanId, Integer period, RepayState repayState);

    RepayExtraGuaranteePlan findByRepayRecordIdAndPeriodAndPlanState(String customerId, Integer period, RepayState repayState);

    Boolean existsByLoanIdAndPeriodAndRepayPurposeAndPlanState(String loanId, Integer period, RepayPurpose repayPurpose, RepayState repayState);

    RepayExtraGuaranteePlan findByLoanIdAndPeriodAndRepayPurpose(String loanId, Integer period, RepayPurpose repayPurpose);


    RepayExtraGuaranteePlan findByLoanIdAndRepayPurposeAndPlanState(String loanId, RepayPurpose repayPurpose, RepayState repayState);

    List<RepayExtraGuaranteePlan> findByLoanId(String loanId);

    List<RepayExtraGuaranteePlan> findByPlanRepayDateBeforeAndRepayPurposeAndPlanStateOrderByIdAsc(
            LocalDate planRepayDate, RepayPurpose repayPurpose, RepayState planState, Pageable pageable);

}
