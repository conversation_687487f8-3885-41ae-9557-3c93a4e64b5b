package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.BankCardRelation;
import com.maguo.loan.cash.flow.entity.BankList;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;


public interface BankCardRelationRepository extends JpaRepository<BankCardRelation, Integer> {

    Optional<BankCardRelation> findByCardNoPrefixSix(String cardNoPrefixSix);

}
