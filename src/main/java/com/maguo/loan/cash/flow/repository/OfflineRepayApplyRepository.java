package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.OfflineRepayApply;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 */
public interface OfflineRepayApplyRepository extends JpaRepository<OfflineRepayApply, String> {

    int countByLoanIdAndPeriod(String loanId, Integer period);
    boolean existsByLoanIdAndPeriod(String loanId, Integer period);

    OfflineRepayApply findByLoanIdAndPeriodAndApplyState(String loanId, Integer period, ProcessState state);

    OfflineRepayApply findByOuterRepayNo(String outerRepayNo);
}
