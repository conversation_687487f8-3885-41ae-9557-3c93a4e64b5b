package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.LvxinRebindRecord;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR> gale
 * @Description
 * @Date 2024/2/19 16:26
 */
public interface LvxinRebindRecordRepository extends JpaRepository<LvxinRebindRecord, String> {

    LvxinRebindRecord findByCreditIdAndStateAndBoundSide(String creditId, ProcessState state, BoundSide boundSide);
}
