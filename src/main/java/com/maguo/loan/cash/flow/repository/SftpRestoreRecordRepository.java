package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.SftpRestoreRecord;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.LoanStage;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;


public interface SftpRestoreRecordRepository extends JpaRepository<SftpRestoreRecord, String> {

    /**
     * 查询指定订单号且7天内的记录
     */
    List<SftpRestoreRecord> findByOuterOrderIdAndFileTypeAndApplyTimeAfterOrderByCreatedTimeDesc(String outerOrderId,FileType fileType, LocalDateTime date);
}
