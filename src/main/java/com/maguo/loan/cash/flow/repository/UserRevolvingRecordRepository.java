package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.UserRevolvingRecord;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * UserRevolvingRecord 持久层接口
 */
public interface UserRevolvingRecordRepository extends JpaRepository<UserRevolvingRecord, String> {

    List<UserRevolvingRecord> findAllByUserIdOrderByAdjustTimeDesc(String userId);
}
