package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.ApprovalRecord;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface ApprovalRecordRepository extends JpaRepository<ApprovalRecord, String> {

    Optional<ApprovalRecord> findByPartnerOrderNoAndFlowChannel(String partnerOrderNo, FlowChannel flowChannel);
}
