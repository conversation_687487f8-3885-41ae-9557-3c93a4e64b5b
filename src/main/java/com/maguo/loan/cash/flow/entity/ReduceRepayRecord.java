package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.ReduceType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * <p>
 * 对客减免记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Entity
@Table(name = "reduce_repay_record")
public class ReduceRepayRecord extends BaseEntity {

    /**
     * 借据
     */
    private String loanId;

    /**
     * 期次
     */
    private Integer period;

    /**
     * NONE-无减免 COUPON-优惠券减免 CALLECTION-催收减免 MARKET-营销减免
     */
    @Enumerated(EnumType.STRING)
    private ReduceType reduceType;

    /**
     * 减免总金额
     */
    private BigDecimal reduceAmount;

    /**
     * 减免本金
     */
    private BigDecimal reducePrincipalAmt;

    /**
     * 减免利息
     */
    private BigDecimal reduceInterestAmt;

    /**
     * 减免罚息
     */
    private BigDecimal reducePenaltyAmt;

    /**
     * 减免资方罚息
     */
    private BigDecimal reduceCapitalPenaltyAmt;

    /**
     * 减免融担罚息
     */
    private BigDecimal reduceGuaranteePenaltyAmt;

    /**
     * 减免咨询费
     */
    private BigDecimal reduceConsultFeeAmt;

    /**
     * 减免担保费
     */
    private BigDecimal reduceGuaranteeFeeAmt;

    /**
     * 减免违约金
     */
    private BigDecimal reduceBreachAmt;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public ReduceType getReduceType() {
        return reduceType;
    }

    public void setReduceType(ReduceType reduceType) {
        this.reduceType = reduceType;
    }

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public BigDecimal getReducePrincipalAmt() {
        return reducePrincipalAmt;
    }

    public void setReducePrincipalAmt(BigDecimal reducePrincipalAmt) {
        this.reducePrincipalAmt = reducePrincipalAmt;
    }

    public BigDecimal getReduceInterestAmt() {
        return reduceInterestAmt;
    }

    public void setReduceInterestAmt(BigDecimal reduceInterestAmt) {
        this.reduceInterestAmt = reduceInterestAmt;
    }

    public BigDecimal getReducePenaltyAmt() {
        return reducePenaltyAmt;
    }

    public void setReducePenaltyAmt(BigDecimal reducePenaltyAmt) {
        this.reducePenaltyAmt = reducePenaltyAmt;
    }

    public BigDecimal getReduceCapitalPenaltyAmt() {
        return reduceCapitalPenaltyAmt;
    }

    public void setReduceCapitalPenaltyAmt(BigDecimal reduceCapitalPenaltyAmt) {
        this.reduceCapitalPenaltyAmt = reduceCapitalPenaltyAmt;
    }

    public BigDecimal getReduceGuaranteePenaltyAmt() {
        return reduceGuaranteePenaltyAmt;
    }

    public void setReduceGuaranteePenaltyAmt(BigDecimal reduceGuaranteePenaltyAmt) {
        this.reduceGuaranteePenaltyAmt = reduceGuaranteePenaltyAmt;
    }

    public BigDecimal getReduceConsultFeeAmt() {
        return reduceConsultFeeAmt;
    }

    public void setReduceConsultFeeAmt(BigDecimal reduceConsultFeeAmt) {
        this.reduceConsultFeeAmt = reduceConsultFeeAmt;
    }

    public BigDecimal getReduceGuaranteeFeeAmt() {
        return reduceGuaranteeFeeAmt;
    }

    public void setReduceGuaranteeFeeAmt(BigDecimal reduceGuaranteeFeeAmt) {
        this.reduceGuaranteeFeeAmt = reduceGuaranteeFeeAmt;
    }

    public BigDecimal getReduceBreachAmt() {
        return reduceBreachAmt;
    }

    public void setReduceBreachAmt(BigDecimal reduceBreachAmt) {
        this.reduceBreachAmt = reduceBreachAmt;
    }

    @Override
    protected String prefix() {
        return "RRR";
    }

}
