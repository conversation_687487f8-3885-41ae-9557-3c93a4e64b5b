package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.BfTransferRecord;
import org.springframework.data.jpa.repository.JpaRepository;

public interface BfTransferRecordRepository extends JpaRepository<BfTransferRecord, String> {
    BfTransferRecord findByServerTransId(String serverTransId);

    // 查询进度最新的记录
    BfTransferRecord findFirstByServerTransIdOrderByStateDesc(String serverTransId);


}
