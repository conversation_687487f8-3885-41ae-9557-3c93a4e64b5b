package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.RouterCheckRecord;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/12/20
 */
public interface RouterCheckRecordRepository extends JpaRepository<RouterCheckRecord, String> {
    Optional<RouterCheckRecord> findByRouterRecordId(String routerRecordId);
}
