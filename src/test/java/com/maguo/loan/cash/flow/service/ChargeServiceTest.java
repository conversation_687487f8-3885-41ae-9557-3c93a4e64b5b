package com.maguo.loan.cash.flow.service;

import com.maguo.loan.cash.flow.config.BaoFuPayMerchantConfigManager;
import com.zsjz.third.part.LocalCachedMerchantConfigFetcher;
import com.zsjz.third.part.PayException;
import com.zsjz.third.part.PayType;
import com.zsjz.third.part.baofoo.BaoFuBindCardService;
import com.zsjz.third.part.baofoo.BaoFuBindConfirmService;
import com.zsjz.third.part.baofoo.BaoFuChargeQueryService;
import com.zsjz.third.part.baofoo.BaoFuChargeService;
import com.zsjz.third.part.baofoo.vo.BaoFuChargeRequest;
import com.zsjz.third.part.baofoo.vo.BaoFuChargeResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;

/**
 * <AUTHOR> gale
 * @Classname ChargeServiceTest
 * @Description TODO
 * @Date 2025/5/23 17:27
 */
@SpringBootTest
class ChargeServiceTest {

    @Autowired
    private BaoFuBindCardService bindCardService;
    @Autowired
    private BaoFuBindConfirmService bindConfirmService;
    @Autowired
    private BaoFuChargeService chargeService;
    @Autowired
    private BaoFuChargeQueryService chargeQueryService;
    @Autowired
    private BaoFuPayMerchantConfigManager baofooMerchantConfigManager;
    @Autowired
    private LocalCachedMerchantConfigFetcher localCachedMerchantConfigFetcher;

    @Test
    public void charge() throws PayException {
        BaoFuChargeRequest request = new BaoFuChargeRequest();
        request.setPayType(PayType.DIRECT);
        request.setOrderId("123456687450");
        request.setAmount(new BigDecimal("100"));
        request.setProtocolNo("1202505121425013780011012572");
        request.setTerminalId("200006192");
        request.setMerchantId("102005463");
        BaoFuChargeResponse response = chargeService.call(request,localCachedMerchantConfigFetcher,baofooMerchantConfigManager.getPayKeyFetcher("102005741"), baofooMerchantConfigManager.getPayConfig());
        System.out.println(response.isRequestSuccess());
        System.out.println(response.getOrderStatus());
        System.out.println(response.getBizCode());
        System.out.println(response.getMsg());
        System.out.println(response.getPayOrderId());
    }
}
