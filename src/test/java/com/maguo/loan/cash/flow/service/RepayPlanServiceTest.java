package com.maguo.loan.cash.flow.service;

import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.service.common.loan.LoanCommonService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Optional;

/**
 * <AUTHOR> gale
 * @Classname RepayPlanServiceTest
 * @Description TODO
 * @Date 2025/6/5 02:11
 */
@SpringBootTest
class RepayPlanServiceTest {

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private RepayPlanService repayPlanService;


    @Autowired
    private LoanCommonService loanCommonService;

    @Test
    void generateRepayPlan() {
        Optional<Loan> lo250604104640133413059216014631 = loanRepository.findById("LO250604104640133413059216014631");
        if ( lo250604104640133413059216014631.isPresent() ) {
            Loan loan = lo250604104640133413059216014631.get();
            repayPlanService.generateRepayPlan(loan);
        }
    }

    @Test
    public void limitDayLoanFlowChannel() {

        //loanCommonService.limitDayLoanFlowChannel(BigDecimal.valueOf(10000000), FlowChannel.PPCJDL);

    }
}
