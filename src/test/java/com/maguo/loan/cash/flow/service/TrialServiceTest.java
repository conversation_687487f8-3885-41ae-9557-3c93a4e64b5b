package com.maguo.loan.cash.flow.service;

import com.maguo.loan.cash.flow.enums.RepayPurpose;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> gale
 * @Classname TrialServiceTest
 * @Description TODO
 * @Date 2025/6/11 19:10
 */
@SpringBootTest
class TrialServiceTest {

    @Autowired
    private TrialService trialService;

    @Test
    void repayTrial() {
//        trialService.repayTrial("LO250605164139381713230455250116", RepayPurpose.CURRENT, 1);

    }
}