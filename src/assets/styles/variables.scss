// base color
$blue:#324157;
$light-blue:#3A71A8;
$red:#C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow:#FEC171;
$panGreen: #30B08F;

// sidebar
$menuText:#787D84;
$menuActiveText:#1A7EFD;
$subMenuActiveText:#1A7EFD; // https://github.com/ElemeFE/element/issues/12951

$menuBg:#f9f9f9;
$menuHover:#e5e5e5;

$subMenuBg:#F0F0F0;
$subMenuHover:#e5e5e5;

$sideBarWidth: 210px;




// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
