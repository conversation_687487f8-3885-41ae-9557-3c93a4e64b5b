// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-input__inner{
  border-radius: 100px;
  padding: 0 12px;
  border: #e5e5e5 1px solid;
}

.el-textarea__inner{
  border-radius: 10px;
  resize: none;
  padding: 12px;
}

.el-form-item__label{
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.el-form--label-top .el-form-item__label{
  padding: 0;
}

.el-form-item--small.el-form-item{
  margin-bottom: 18px;
}

.el-form--label-top.el-form--inline .el-form-item{
  vertical-align: bottom;
  margin-bottom: 4px;
}

.el-button--mini{
  padding:  9px 12px;
}

.el-button--mini.is-round{
  padding:  9px 20px;
}

.el-table--border th.el-table__cell{
  background: #f5f5f5;
}

.el-table th.el-table__cell{
  background: #f5f5f5;
}
.el-input__prefix{
  left: 10px;
}
