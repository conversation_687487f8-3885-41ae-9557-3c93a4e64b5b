<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="" prop="">
        <el-date-picker v-model="dataVal" size="small" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :picker-options="pickerOptionsDefault" clearable></el-date-picker>
      </el-form-item>
      <el-form-item label="" prop="applyChannel">
        <el-select placeholder="所有渠道" v-model="queryParams.applyChannel" style="width: 200px;" clearable>
          <el-option v-for="item in applyChannelOptions" :label="item.label" :value="item.value" :key="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button round type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <!-- <el-button type="success" size="mini" @click="handleQuery">导出</el-button> -->
      </el-form-item>
    </el-form>

    <el-table border="border" v-loading="loading" :data="list">
      <el-table-column label="日期" prop="statisticDate" align="center"></el-table-column>
      <el-table-column label="进件渠道" prop="applyChannel" align="center" :formatter="applyChannelFormat"></el-table-column>
      <el-table-column label="注册人数" prop="registerNum" align="center"></el-table-column>
      <el-table-column label="申额人数" prop="applyLimitNum" align="center"></el-table-column>
      <el-table-column label="申额拒绝人数" prop="applyLimitRefuseNum" align="center"></el-table-column>
      <el-table-column label="获额人数" prop="applyLimitPassNum" align="center"></el-table-column>
      <el-table-column label="获额金额" prop="applyLimitPassAmount" align="center"></el-table-column>
      <el-table-column label="风控通过人数" prop="riskPassNum" align="center"></el-table-column>
      <el-table-column label="风控通过金额" prop="riskPassAmount" align="center"></el-table-column>
      <el-table-column label="风控拒绝人数" prop="riskRefuseNum" align="center"></el-table-column>
      <el-table-column label="风控拒绝金额" prop="riskRefuseAmount" align="center"></el-table-column>
      <el-table-column label="申请人数" prop="applyNum" align="center"></el-table-column>
      <el-table-column label="申请金额" prop="applyAmount" align="center"></el-table-column>
      <el-table-column label="资方授信人数" prop="capitalCreditNum" align="center"></el-table-column>
      <el-table-column label="资方授信金额" prop="capitalCreditAmount" align="center"></el-table-column>
      <el-table-column label="资方授信中人数" prop="capitalProcessingNum" align="center"></el-table-column>
      <el-table-column label="资方拒绝人数" prop="capitalRefuseNum" align="center"></el-table-column>
      <el-table-column label="资方拒绝金额" prop="capitalRefuseAmount" align="center"></el-table-column>
      <el-table-column label="审批中人数" prop="approveNum" align="center"></el-table-column>
      <el-table-column label="审核中/权益" prop="reviewRights" align="center"></el-table-column>
      <el-table-column label="待要款人数" prop="awaitCollectNum" align="center"></el-table-column>
      <el-table-column label="待要款金额" prop="awaitCollectAmount" align="center"></el-table-column>
      <el-table-column label="待放款人数" prop="awaitLoanNum" align="center"></el-table-column>
      <el-table-column label="待放款金额" prop="awaitLoanAmount" align="center"></el-table-column>
      <el-table-column label="要款失败人数" prop="collectFailNum" align="center"></el-table-column>
      <el-table-column label="要款失败金额" prop="collectFailAmount" align="center"></el-table-column>
      <el-table-column label="放款人数" prop="loanNum" align="center"></el-table-column>
      <el-table-column label="放款成功金额" prop="loanSucceedAmount" align="center"></el-table-column>
      <el-table-column label="件均" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.loanNum">
            {{ (Number(scope.row.loanSucceedAmount || 0) / Number(scope.row.loanNum)).toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="放款失败人数" prop="loanFailNum" align="center"></el-table-column>
      <el-table-column label="放款失败金额" prop="loanFailAmount" align="center"></el-table-column>
      <el-table-column label="获额率" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.applyLimitNum">
            {{ (Number(scope.row.applyLimitPassNum || 0) / Number(scope.row.applyLimitNum)).toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="风控通过率" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.applyLimitNum">
            {{ (Number(scope.row.applyLimitPassNum || 0) / Number(scope.row.applyLimitNum)).toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="资方授信通过率" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.applyNum">
            {{ (Number(scope.row.capitalCreditNum || 0) / Number(scope.row.applyNum)).toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="总通过率" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.applyLimitNum">
            {{ (Number(scope.row.loanNum || 0) / Number(scope.row.applyLimitNum)).toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="生成时间" prop="createdTime" align="center"></el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

  </div>
</template>

<script>
import { drainageQuery } from '@/api/drainage'
import { get as getDictByName } from "@/api/system/dictDetail"
export default {
  name: "",
  data() {
    return {
      loading: false,
      total: 0,
      list: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dataVal: [],
      // 表单校验
      pickerOptionsDefault: {
        onPick: ({ maxDate, minDate }) => {
          //当我们选择两个值的时候，就认为用户已经选择完毕
          if (maxDate != null && minDate != null) {
            this.repayDateBegin = maxDate;
            this.repayDateEnd = minDate;
          }
        },
        disabledDate: time => {
          let maxDate = this.maxDate;
          let minDate = this.minDate;
          if (maxDate != null && minDate != null) {
            let days = maxDate.getTime() - minDate.getTime();
            //计算完之后必须清除，否则选择器一直处于禁止选择的状态
            this.maxDate = null;
            this.minDate = null;
            return parseInt(days / (1000 * 60 * 60 * 24)) > 30;
          } else {
            //设置当前时间后的时间不可选
            return time.getTime() > Date.now();
          }
        }
      },
      applyChannelOptions:[],
    };
  },
  created() {
    getDictByName("applyChannel").then(res => {
      this.applyChannelOptions = res.content;
    });

    this.getList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      drainageQuery(this.queryParams).then(res => {
        this.list = res.data.list;
        this.total = res.data.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      if (this.dataVal && this.dataVal.length > 0) {
        this.queryParams.startDate = this.dataVal[0]
        this.queryParams.endDate = this.dataVal[1]
      } else {
        this.queryParams.startDate = undefined
        this.queryParams.endDate = undefined
      }

      this.queryParams.pageNum = 1;
      this.getList();
    },

    applyChannelFormat({applyChannel}){
      let obj = this.applyChannelOptions.find(item => item.value === applyChannel)
      return obj ? obj.label : '--'
    }
  },
};
</script>