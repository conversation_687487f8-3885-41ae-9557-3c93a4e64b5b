<template>
  <div class="wrapper">
    <div
      v-if="mode !== 'detail'"
      class="tip"
    >
      鼠标拖拽或点击[↑]或[↓]可移动顺序
    </div>

    <el-table
      v-if="mode === 'detail'"
      v-loading="loading"
      border="border"
      :data="data"
    >
      <el-table-column
        label="优先级"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="通道名称"
        prop="paymentChannelName"
        align="center"
      />
      <el-table-column
        label="每日扣款笔数上限"
        prop="dailyUpperLimit"
        align="center"
      />
      <el-table-column
        label="手续费补贴"
        prop="feeSubsidy"
        align="center"
      />
    </el-table>

    <div
      v-else
      class="table-wrap"
    >
      <el-row class="row table-head">
        <el-col :span="3">
          优先级
        </el-col>
        <el-col :span="4">
          通道名称
        </el-col>
        <el-col :span="6">
          每日扣款笔数上限
        </el-col>
        <el-col :span="5">
          手续费补贴
        </el-col>
        <el-col
          v-if="mode !== 'detail'"
          :span="6"
        >
          操作
        </el-col>
      </el-row>
      <draggable v-model="data">
        <el-row
          v-for="(item, index) in data"
          :key="index"
          class="row"
        >
          <el-col
            :span="3"
            :style="{ opacity: item.disabled ? '0.3' : 1 }"
          >
            {{
              index + 1
            }}
          </el-col>
          <el-col
            :span="4"
            :style="{ opacity: item.disabled ? '0.3' : 1 }"
          >
            {{ item.paymentChannelName }}
          </el-col>
          <el-col
            :span="6"
            :style="{ opacity: item.disabled ? '0.3' : 1 }"
          >
            <el-input-number
              v-model="item.dailyUpperLimit"
              style="width: 100%"
              controls-position="right"
              placeholder="请填写"
              :disabled="mode === 'detail' || item.disabled"
              :min="1"
            />
          </el-col>
          <el-col
            :span="5"
            :style="{ opacity: item.disabled ? '0.3' : 1 }"
          >
            <el-input-number
              v-model="item.feeSubsidy"
              style="width: 100%"
              controls-position="right"
              placeholder="请填写"
              :disabled="mode === 'detail' || item.disabled"
              :min="0"
            />
          </el-col>
          <el-col
            v-if="mode !== 'detail'"
            :span="6"
          >
            <i
              class="el-icon-top icon"
              :class="{ disabled: index === 0 }"
              @click="swap(index, -1)"
            />
            <i
              class="el-icon-bottom icon"
              :class="{ disabled: index === data.length - 1 }"
              @click="swap(index, 1)"
            />
            <el-button
              type="text"
              :style="{ color: item.disabled ? '#1a7efd' : '#c00' }"
              @click="toggleItem(item)"
            >
              {{ item.disabled ? "启用" : "停用" }}
            </el-button>
            <el-button
              v-if="item.disabled"
              style="color: #c00"
              type="text"
              @click="delItem(item)"
            >
              删除
            </el-button>
          </el-col>
        </el-row>
      </draggable>
      <div class="new-btn">
        <el-popover
          v-model="visible"
          placement="bottom-right"
          trigger="manual"
        >
          <div class="popover-content">
            <div
              v-for="item in capitalList"
              :key="item.paymentChannelCode"
              class="option-item"
            >
              <el-checkbox
                v-model="item.checked"
                :disabled="disabledCodes.includes(item.paymentChannelCode)"
                :label="item.paymentChannelCode"
                class="option-item"
              >
                {{ item.paymentChannelName }}
              </el-checkbox>
            </div>
            <div class="confirm-btn">
              <el-button
                type="text"
                style="padding: 5px"
                size="mini"
                @click="addItemOk"
              >
                确定
              </el-button>
            </div>
          </div>
          <el-button
            v-if="mode !== 'detail'"
            slot="reference"
            type="text"
            size="mini"
            @click="addItem"
          >
            新增通道
          </el-button>
        </el-popover>
      </div>
    </div>
  </div>
</template>

<script>
import Draggable from 'vuedraggable'
import { queryPaymentChannelOptions } from '@/api/setting/deduct'

export default {
  name: 'TableForm',
  components: {
    Draggable
  },
  model: {
    prop: 'data',
    event: 'updateData'
  },
  props: {
    mode: {
      type: String,
      required: true
    },
    data: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      visible: false,
      capitalList: [],
      selectedCapitalList: []
    }
  },
  computed: {
    disabledCodes() {
      return this.data.map((item) => item.paymentChannelCode)
    }
  },
  created() {
    this.handleGetCapitalList()
  },
  destroyed() {
    this.visible = false
    this.capitalList = []
    this.selectedCapitalList = []
  },
  methods: {
    async handleGetCapitalList() {
      const result = await queryPaymentChannelOptions()

      this.capitalList = result.data.map((item) => ({
        dailyUpperLimit: item.dailyUpperLimit,
        feeSubsidy: item.feeSubsidy,
        paymentChannelCode: item.paymentChannelCode,
        paymentChannelName: item.paymentChannelName,
        checked: this.data.some((o) => o.paymentChannelCode === item.paymentChannelCode)
      }))
    },
    async addItem() {
      this.handleGetCapitalList()
      this.visible = true
    },
    addItemOk() {
      const selectedOptions = this.capitalList.filter((item) => item.checked)

      const nextList = [...this.data]

      selectedOptions.forEach((item) => {
        if (!nextList.some((o) => o.paymentChannelCode === item.paymentChannelCode)) {
          nextList.push(item)
        }
      })

      this.$emit('updateData', [...nextList])

      this.visible = false
    },
    toggleItem(row) {
      const nextList = this.data.map((o) => ({
        ...o,
        disabled: o.paymentChannelCode === row.paymentChannelCode ? !o.disabled : (o.disabled || false),
        dailyUpperLimit: o.dailyUpperLimit === undefined ? 1 : o.dailyUpperLimit,
        feeSubsidy: o.feeSubsidy === undefined ? 0 : o.feeSubsidy,
      }))

      this.$emit('updateData', [...nextList])
    },
    delItem(row) {
      this.$confirm('是否确认删除该通道', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        roundButton: true,
        type: 'warning'
      }).then(() => {
        const targetIndex = this.capitalList.findIndex(
          (o) => o.paymentChannelCode === row.paymentChannelCode
        )

        this.capitalList[targetIndex].checked = false

        const nextList = this.data.filter(
          (item) => item.paymentChannelCode !== row.paymentChannelCode
        )

        this.$emit('updateData', [...nextList])
      })
    },
    swap(index, direction) {
      const newIndex = index + direction

      if (newIndex >= 0 && newIndex < this.data.length) {
        [this.data[index], this.data[newIndex]] =
          [this.data[newIndex], this.data[index]]

        this.$forceUpdate()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tip {
  font-size: 12px;
  color: #999;
  margin-bottom: 16px;
}
.table-wrap {
  width: 100%;
  border-left: #e5e5e5 1px solid;
  border-right: #e5e5e5 1px solid;
}
.table-head {
  background: #f5f5f5;
  font-weight: bold;
}
.row {
  width: 100%;
  border-bottom: #e5e5e5 1px solid;

  &:hover {
    background: #e5e5e5;
  }

  .el-col {
    padding: 10px;
  }

  .dis-tip {
    font-style: normal;
    font-size: 12px;
    background: #f5f5f5;
    display: inline-block;
    padding: 4px 4px;
    border-radius: 4px;
    border: #e5e5e5 1px solid;
    line-height: 1;
    margin-left: 8px;
  }
}
.table-body {
  width: 100%;
}

.new-btn {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-bottom: #e5e5e5 1px solid;
}

.option-item {
  display: flex;
  height: 28px;
  display: flex;
  align-items: center;
  line-height: 30px;
}

.confirm-btn {
  display: flex;
  justify-content: flex-end;
  padding: 6px 0 6px 0;
}

.icon {
  margin-right: 10px;
  color: #1a7efd;
  cursor: pointer;
  &.disabled {
    color: #e5e5e5;
    cursor: not-allowed;
  }
}
</style>
