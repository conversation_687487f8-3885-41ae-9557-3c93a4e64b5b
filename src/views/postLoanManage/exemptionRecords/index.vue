<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item>
        <el-select v-model="queryParams.type" placeholder="请选择" style="width: 100px;">
          <el-option label="手机号" value="mobile"></el-option>
          <el-option label="订单编号" value="orderId"></el-option>
          <el-option label="身份证号" value="certNo"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入" clearable v-model="queryParams.typeText" style="width:200px;" />
      </el-form-item>
      <!-- <el-form-item>
        <el-select v-model="queryParams.orderState" placeholder="减免类型" style="width: 160px;" clearable>
          <el-option v-for="item in orderStateOptions" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-select v-model="queryParams.useState" placeholder="使用状态" style="width: 160px;" clearable>
          <el-option label="待使用" value="WAIT"></el-option>
          <el-option label="已使用" value="USED"></el-option>
          <el-option label="已过期" value="EXPIRED"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="queryParams.auditState" placeholder="审批状态" style="width: 160px;" clearable>
          <el-option label="待审核" value="WAIT"></el-option>
          <el-option label="审核通过" value="PASS"></el-option>
          <el-option label="审核拒绝" value="REFUSE"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="dataVal" size="small" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
          :picker-options="pickerOptionsDefault"></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button round type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table border="border" :data="list" v-loading="loading">
      <el-table-column label="订单编号" align="center" fixed width="280">
        <template slot-scope="scope">
          <el-link :underline="false" type="primary" @click="getPlan(scope.row)">{{ scope.row.orderId }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="借款本金" prop="principalAmt" align="center"></el-table-column>
      <el-table-column label="还款期数" prop="period" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.repayPurpose === 'CLEAR'">提前结清</span>
          <span v-if="scope.row.repayPurpose === 'CURRENT'">{{ scope.row.period }}</span>
        </template>
      </el-table-column>
      <el-table-column label="减免金额" prop="reduceAmount" align="center"></el-table-column>
      <el-table-column label="使用状态" prop="useState" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.useState === 'WAIT'">待使用</span>
          <span v-if="scope.row.useState === 'USED'">已使用</span>
          <span v-if="scope.row.useState === 'EXPIRED'">已过期</span>
        </template>
      </el-table-column>
      <el-table-column label="审批状态" prop="auditState" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.auditState === 'WAIT'">待审核</span>
          <span v-if="scope.row.auditState === 'PASS'">审核通过</span>
          <span v-if="scope.row.auditState === 'REFUSE'">审核拒绝</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createdBy" align="center"></el-table-column>
      <el-table-column label="审批人" prop="updatedBy" align="center"></el-table-column>
      <el-table-column label="减免备注" prop="remark" align="center"></el-table-column>
      <el-table-column label="创建时间" prop="createdTime" align="center"></el-table-column>
      <el-table-column label="更新时间" prop="updatedTime" align="center"></el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="200">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.auditState === 'WAIT'" size="mini" @click="handleAudit(scope.row.id, 'PASS')">审批通过</el-button>
          <el-button type="text" v-if="scope.row.auditState === 'WAIT'" size="mini" @click="handleAudit(scope.row.id, 'REFUSE')">审批拒绝</el-button>
          <el-button style="color: #c00;" type="text" v-if="scope.row.useState === 'WAIT' && scope.row.auditState === 'PASS'" size="mini" @click="handleCancel(scope.row.id)">取消审批</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 还款计划 -->
    <el-dialog title="还款计划" :visible.sync="visible" width="1200px">
      <div v-if="detail">
        <el-table border="border" :data="detail.repayPlanList">
          <el-table-column label="期数" prop="period" align="center"></el-table-column>
          <el-table-column label="应还时间" prop="planRepayDate" align="center"></el-table-column>
          <el-table-column label="实还时间" prop="actRepayTime" align="center"></el-table-column>
          <el-table-column label="当期状态" prop="nowRepayState" align="center"></el-table-column>
          <el-table-column label="还款状态" prop="custRepayState" align="center"></el-table-column>
          <el-table-column label="应还本金" prop="principalAmt" align="center"></el-table-column>
          <el-table-column label="应还利息" prop="interestAmt" align="center"></el-table-column>
          <el-table-column label="应还罚息" prop="penaltyAmt" align="center"></el-table-column>
          <el-table-column label="应还融担费" prop="guaranteeAmt" align="center"></el-table-column>
          <el-table-column label="应还服务费" prop="consultFee" align="center"></el-table-column>
          <el-table-column label="应还违约金" prop="breachFee" align="center"></el-table-column>
          <el-table-column label="实还本金" prop="actPrincipalAmt" align="center"></el-table-column>
          <el-table-column label="实还利息" prop="actInterestAmt" align="center"></el-table-column>
          <el-table-column label="实还罚息" prop="actPenaltyAmt" align="center"></el-table-column>
          <el-table-column label="实还融担费" prop="actGuaranteeAmt" align="center"></el-table-column>
          <el-table-column label="实还服务费" prop="actConsultFee" align="center"></el-table-column>
          <el-table-column label="实还违约金" prop="actBreachFee" align="center"></el-table-column>
          <el-table-column label="减免金额" prop="reduceAmount" align="center"></el-table-column>
        </el-table>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { offlineRepayReduceSearchPage, audit, queryRepayPlan } from "@/api/postLoanManage";
// import { get as getDictByName } from "@/api/system/dictDetail"

export default {
  name: "",
  data() {
    return {
      // 查询参数
      queryParams: {
        type: "mobile",
        typeText: "",
        pageNum: 1,
        pageSize: 10,
        useState: undefined,
        auditState: undefined,
        beginTime: undefined,
        endTime: undefined,
      },
      loading: false,
      list: [],
      total: 0,
      dataVal: [],
      pickerOptionsDefault: {
        onPick: ({ maxDate, minDate }) => {
          //当我们选择两个值的时候，就认为用户已经选择完毕
          if (maxDate != null && minDate != null) {
            this.repayDateBegin = maxDate;
            this.repayDateEnd = minDate;
          }
        },
        disabledDate: time => {
          let maxDate = this.maxDate;
          let minDate = this.minDate;
          if (maxDate != null && minDate != null) {
            let days = maxDate.getTime() - minDate.getTime();
            //计算完之后必须清除，否则选择器一直处于禁止选择的状态
            this.maxDate = null;
            this.minDate = null;
            return parseInt(days / (1000 * 60 * 60 * 24)) > 30;
          } else {
            //设置当前时间后的时间不可选
            return time.getTime() > Date.now();
          }
        }
      },
      visible: false,
      detail:undefined,
    };
  },
  created() {
    this.getList()
  },
  methods: {
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 获取订单列表
    getList() {
      this.loading = true;

      let params = {
        mobile:
          this.queryParams.type === "mobile"
            ? this.queryParams.typeText
            : undefined,
        orderId:
          this.queryParams.type === "orderId"
            ? this.queryParams.typeText
            : undefined,
        certNo:
          this.queryParams.type === "certNo"
            ? this.queryParams.typeText
            : undefined,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        useState: this.queryParams.useState,
        auditState: this.queryParams.auditState,
      };

      if (this.dataVal && this.dataVal.length === 2) {
        params.beginTime = this.dataVal[0]
        params.endTime = this.dataVal[1]
      } else {
        params.beginTime = undefined
        params.endTime = undefined
      }

      offlineRepayReduceSearchPage(params).then(res => {
        this.list = res.data.list;
        this.total = res.data.total;
        this.loading = false;
      });
    },

    // 审核
    handleAudit(id, auditState) {
      this.$confirm(`是否确认${auditState === 'PASS' ? '审批通过' : '审批拒绝'}该订单?`, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        roundButton: true,
        type: "warning"
      }).then(() => {
        let params = {
          reduceId: id,
          auditState
        }
        audit(params).then(res => {
          this.$message.success('操作成功')
          this.getList()
        })
      })
    },

    // 还款计划
    getPlan(row){
      queryRepayPlan({orderId: row.orderId}).then(res => {
        this.detail = res.data
        this.visible = true
      });
    },

    // 取消审批
    handleCancel(id){
      this.$confirm(`是否确认取消审批该订单?`, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        roundButton: true,
        type: "warning"
      }).then(() => {
        let params = {
          reduceId: id,
          auditState: 'REVOKE'
        }
        audit(params).then(res => {
          this.$message.success('操作成功')
          this.getList()
        })
      })
    }
  }
};
</script>
