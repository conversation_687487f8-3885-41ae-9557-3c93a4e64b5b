<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item>
        <el-select v-model="queryParams.type" placeholder="请选择" style="width: 100px">
          <el-option label="手机号" value="mobile"></el-option>
          <el-option label="订单编号" value="orderId"></el-option>
          <el-option label="身份证号" value="certNo"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入" clearable v-model="queryParams.typeText" style="width: 290px" />
      </el-form-item>
      <el-form-item>
        <el-button round type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>
    <div v-if="detail">
      <div style="font-size: 14px; margin-bottom: 10px">
        <span style="margin-right: 16px">订单号：{{ detail.orderId }}</span>
        <span style="margin-right: 16px">用户姓名：{{ detail.userName }}</span>
        <span style="margin-right: 16px">用户身份证号：{{ detail.certNo }}</span>
      </div>
      <div style="font-size: 14px; margin-bottom: 10px">还款计划</div>
      <div style="margin-bottom: 10px">
        <el-table border="border" :data="detail.repayPlanList" v-loading="loading">
          <el-table-column label="期数" prop="period" align="center"></el-table-column>
          <el-table-column label="应还时间" prop="planRepayDate" align="center"></el-table-column>
          <el-table-column label="实还时间" prop="actRepayTime" align="center"></el-table-column>
          <el-table-column label="当期状态" prop="nowRepayState" align="center"></el-table-column>
          <el-table-column label="还款状态" prop="custRepayState" align="center"></el-table-column>
          <el-table-column label="应还本金" prop="principalAmt" align="center"></el-table-column>
          <el-table-column label="应还利息" prop="interestAmt" align="center"></el-table-column>
          <el-table-column label="应还罚息" prop="penaltyAmt" align="center"></el-table-column>
          <el-table-column label="应还融担费" prop="guaranteeAmt" align="center"></el-table-column>
          <el-table-column label="应还服务费" prop="consultFee" align="center"></el-table-column>
          <el-table-column label="应还违约金" prop="breachFee" align="center"></el-table-column>
          <el-table-column label="实还本金" prop="actPrincipalAmt" align="center"></el-table-column>
          <el-table-column label="实还利息" prop="actInterestAmt" align="center"></el-table-column>
          <el-table-column label="实还罚息" prop="actPenaltyAmt" align="center"></el-table-column>
          <el-table-column label="实还融担费" prop="actGuaranteeAmt" align="center"></el-table-column>
          <el-table-column label="实还服务费" prop="actConsultFee" align="center"></el-table-column>
          <el-table-column label="实还违约金" prop="actBreachAmt" align="center"></el-table-column>
          <el-table-column label="减免金额" prop="reduceAmount" align="center"></el-table-column>
          <el-table-column label="还款失败原因" prop="failMsg" align="center"></el-table-column>
        </el-table>
      </div>

      <div style="margin-bottom: 10px">
        <el-button v-permission="['admin', 'loanManagement:trial']" round type="primary" plain
          @click="handleTrial()">结清试算</el-button>
        <el-button v-permission="['admin', 'loanManagement:reduce']" round type="primary" plain
          @click="handleReduceApply()">减免申请</el-button>
        <el-button v-permission="['admin', 'loanManagement:writeOff']" round type="primary" plain
          @click="handleRepayApplyPreview()">还款销账</el-button>
        <el-button v-permission="['admin', 'loanManagement:remark']" round type="primary" plain>订单备注</el-button>
        <el-button round type="primary" plain @click="handleAggregatePay('APP')">聚合支付/APP</el-button>
        <el-button round type="primary" plain @click="handleAggregatePay('H5')">聚合支付/H5</el-button>
      </div>
    </div>

    <!-- 结清试算 -->
    <div v-if="showIndex === 1">
      <div style="margin-bottom: 10px" v-if="trialRes">
        <el-table border="border" :data="trialRes">
          <el-table-column label="本金" prop="principal" align="center"></el-table-column>
          <el-table-column label="利息" prop="interest" align="center"></el-table-column>
          <el-table-column label="罚息" prop="penalty" align="center"></el-table-column>
          <el-table-column label="违约金" prop="breachFee" align="center"></el-table-column>
          <el-table-column label="融担费" prop="guaranteeFee" align="center"></el-table-column>
          <el-table-column label="咨询费" prop="consultFee" align="center"></el-table-column>
          <el-table-column label="应还金额" prop="amount" align="center"></el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 减免申请 -->
    <div v-if="showIndex === 2">
      <div style="margin-bottom: 10px; background: #f5f5f5; padding: 16px">
        <el-form :model="form" label-width="120px">
          <el-form-item label="减免期数">
            <el-radio-group v-model="form.type" @change="change1">
              <el-radio :label="1">{{
      `当期 ${currentPeriod ? `(第${currentPeriod}期)` : ""}`
    }}</el-radio>
              <el-radio :label="2">结清</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="计划金额">
            <span>{{ currentAmount }}元</span>
          </el-form-item>
          <el-form-item label="减免金额">
            <el-input type="number" v-model="form.reduceAmount"></el-input>
          </el-form-item>
          <el-form-item label="减免后应还金额">
            <span>{{
      (currentAmount - Number(form.reduceAmount)).toFixed(2)
    }}</span>
          </el-form-item>
          <el-form-item label="申请备注">
            <el-input type="textarea" v-model="form.remark"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button round type="primary" @click="onSubmit" :disabled="currentPeriod === null">确认申请</el-button>
            <el-button round @click="restForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 还款销账 -->
    <div v-if="showIndex === 3">
      <div style="margin-bottom: 10px; background: #f5f5f5; padding: 16px">
        <el-form :model="form1" label-width="120px">
          <el-form-item label="还款期数">
            <el-radio-group v-model="form1.type" @change="change2">
              <el-radio :label="1">{{
      `当期 ${currentPeriod ? `(第${currentPeriod}期)` : ""}`
    }}</el-radio>
              <el-radio :label="2">结清</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="客户应还金额">
            <el-input v-model="form1.amount" disabled></el-input>
          </el-form-item>
          <el-form-item label="减免金额">
            <el-input v-model="form1.reduceAmount" disabled></el-input>
          </el-form-item>
          <el-form-item label="实还金额">
            <el-input v-on:input="validateInput" @keydown="handleKeyDown" v-model="form1.actAmount"
              :disabled="actAmountDisabled"></el-input>
          </el-form-item>
          <el-form-item label="溢出金额" v-if="Number(form1.reduceAmount) === 0">
            <el-input v-model="overflowAmount" disabled></el-input>
          </el-form-item>
          <el-form-item>
            <el-button round type="primary" @click="onSubmit1" :disabled="currentPeriod === null">销账</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 订单备注 -->
    <div v-if="showIndex === 4">
      <div style="margin-bottom: 10px; background: #f5f5f5; padding: 16px">
        <div style="display: flex; justify-content: flex-end; margin-bottom: 10px">
          <el-button round type="primary">新增备注</el-button>
        </div>
        <el-table border="border">
          <el-table-column label="还款编号" prop="" align="center"></el-table-column>
          <el-table-column label="操作账户" prop="" align="center"></el-table-column>
          <el-table-column label="类型" prop="" align="center"></el-table-column>
          <el-table-column label="备注" prop="" align="center"></el-table-column>
          <el-table-column label="创建时间" prop="" align="center"></el-table-column>
          <el-table-column label="更新时间" prop="" align="center"></el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 聚合支付 -->
    <div v-if="showIndex === 5">
      <div style="margin-bottom: 10px; background: #f5f5f5; padding: 16px">
        <el-form :model="form2" :rules="rules2" label-width="120px" ref="form2">
          <el-form-item label="支付渠道" v-if="mode === 'H5'">
            <el-radio-group v-model="form2.payChannel">
              <el-radio label="YIBEIJIA_SCAN">益倍嘉-扫码付</el-radio>
              <el-radio label="BAOFOO_TRANS_PAY">宝付</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="还款期数">
            <el-radio-group v-model="form2.type" @change="change3">
              <el-radio :label="1">{{
      `当期 ${currentPeriod ? `(第${currentPeriod}期)` : ""}`
    }}</el-radio>
              <el-radio :label="2">结清</el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="form2.payChannel === 'BAOFOO_TRANS_PAY'">
            <el-form-item label="银行卡号" prop="payerAcctCode">
              <el-input v-model="form2.payerAcctCode"></el-input>
            </el-form-item>
            <el-form-item label="银行卡姓名" prop="payerUserName">
              <el-input v-model="form2.payerUserName"></el-input>
            </el-form-item>
          </template>
          <el-form-item label="客户应还金额">
            <el-input v-model="form2.amount" disabled></el-input>
          </el-form-item>
          <el-form-item label="减免金额">
            <el-input v-model="form2.reduceAmount" disabled></el-input>
          </el-form-item>
          <el-form-item label="客户实还金额">
            <el-input v-model="form2.actAmount" disabled></el-input>
          </el-form-item>
          <el-form-item>
            <el-button v-if="mode === 'APP'" round type="primary" @click="onSubmitForm2">提交订单</el-button>
            <el-button v-if="mode === 'H5'" round type="primary" @click="onSubmitForm2H5">
              {{
      form2.payChannel === "YIBEIJIA_SCAN"
        ? "生成支付单"
        : "去宝付提款"
    }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 新增备注 -->
    <el-dialog title="新增备注" :visible.sync="remarkDialogVisible" width="500px" :before-close="closeRemark">
      <el-form :model="ruleFormRemark" :rules="rulesRemark" ref="ruleFormRemark" label-width="100px">
        <el-form-item label="订单编号" prop="orderNo">
          <el-input v-model="ruleFormRemark.orderNo" disabled></el-input>
        </el-form-item>
        <el-form-item label="还款编号" prop="">
          <el-input v-model="ruleFormRemark.orderNo" disabled></el-input>
        </el-form-item>
        <el-form-item label="客户手机" prop="mobile">
          <el-input v-model="ruleFormRemark.mobile" disabled></el-input>
        </el-form-item>
        <el-form-item label="客户姓名" prop="name">
          <el-input v-model="ruleFormRemark.name" disabled></el-input>
        </el-form-item>
        <el-form-item label="资金渠道" prop="bankChannel">
          <el-input v-model="ruleFormRemark.bankChannel" disabled></el-input>
        </el-form-item>
        <el-form-item label="备注类型" prop="remarkType">
          <el-select v-model="ruleFormRemark.remarkType" placeholder="请选择" style="width: 100%">
            <el-option label="订单备注" value="订单备注"></el-option>
            <el-option label="工单备注" value="工单备注"></el-option>
            <el-option label="催收备注" value="催收备注"></el-option>
            <el-option label="投诉备注" value="投诉备注"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单备注" prop="remark">
          <el-input v-model="ruleFormRemark.remark"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button round @click="closeRemark">取消</el-button>
          <!-- <el-button round type="primary" @click="submitFormRemark">确认</el-button> -->
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 二维码弹窗 -->
    <el-dialog title="普通支付" :visible.sync="dialogCodeVisible" width="370px" :before-close="closeCodeDialog">
      <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        ">
        <div style="margin-bottom: 10px">
          <span>普通支付金额: {{ codeRes.actAmount }}元</span>
        </div>
        <div style="margin-bottom: 10px">
          <span>订单号: {{ codeRes.orderId }}</span>
        </div>
        <div style="margin-bottom: 10px">
          <span>用户姓名: {{ codeRes.name }}</span>
        </div>
        <div style="margin-bottom: 10px">请将二维码截图给用户进行扫码</div>
        <div style="position: relative; width: 300px">
          <img src="@/assets/images/code_bg.png" alt="" style="width: 100%; height: auto" />
          <img :src="'data:image/png;base64,' + codeRes.qrCodeBase64" alt="" style="
              width: 200px;
              height: 200px;
              display: block;
              position: absolute;
              left: 50px;
              top: 110px;
            " />
        </div>
      </div>
      <div style="display: flex; justify-content: center">
        <el-button round type="primary" size="mini" @click="openSend">发送支付链接</el-button>
      </div>
    </el-dialog>

    <!-- 发送弹窗 -->
    <el-dialog title="确认弹窗" :visible.sync="dialogSendVisible" width="370px" :before-close="closeSendDialog">
      <div style="font-size: 14px; margin-bottom: 10px">
        确认向该手机发送支付链接？
      </div>
      <el-input style="margin-bottom: 16px" placeholder="请输入" v-model="phone" />
      <div style="display: flex; justify-content: flex-end">
        <el-button round type="primary" size="mini" @click="send">发送</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryRepayPlan,
  trial,
  reduceApply,
  repayApplyPreview,
  repayApply,
  aggregatePayApply,
  sendPayUrlSms,
} from "@/api/postLoanManage";
import * as math from "mathjs";
// import { get as getDictByName } from "@/api/system/dictDetail"

const deBounce = function deBounce(func, wait) {
  let timeOut = null;
  return (args) => {
    clearTimeout(timeOut);
    timeOut = setTimeout(() => func(args), wait);
  };
};

export default {
  name: "",
  data() {
    return {
      // 查询参数
      queryParams: {
        type: "mobile",
        typeText: "",
      },
      loading: false,

      detail: undefined, // 还款计划
      trialRes: undefined, //试算结果

      currentPeriod: null, //当期
      currentAmount: null, // 计划金额

      form: {
        type: 1,
        reduceAmount: 0,
        remark: "",
      },

      form1: {
        type: 1,
        period: null,
        amount: null, // 客户应还金额
        reduceAmount: null, // 减免金额
        actAmount: null, //实还金额
      },
      actAmountDisabled: false,

      form2: {
        type: 1,
        period: null,
        amount: null, // 应还金额
        reduceAmount: null, // 减免金额
        actAmount: null, //实还金额
        payerAcctCode: undefined,
        payerUserName: undefined,
      },
      rules2: {
        payerAcctCode: [
          { required: true, message: "不能为空", trigger: "blur" },
          { validator: this.checkInteger, trigger: "blur" },
        ],
        payerUserName: [
          { required: true, message: "不能为空", trigger: "blur" },
        ],
      },
      form2Table: [
        // {
        //   period: 1,
        //   amount: 1200, // 应还金额
        //   reduceAmount: 200, // 减免金额
        //   actAmount: 300, //实还金额
        // }
      ],
      mode: "",
      dialogCodeVisible: false,
      codeRes: {},
      dialogSendVisible: false,
      phone: "",

      // 备注弹窗
      remarkDialogVisible: false,
      ruleFormRemark: {},
      rulesRemark: {
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        mobile: [
          { required: true, message: "手机号不能为空", trigger: "change" },
        ],
        orderNo: [
          { required: true, message: "订单号不能为空", trigger: "change" },
        ],
        bankChannel: [
          { required: true, message: "资方渠道不能为空", trigger: "change" },
        ],
        remark: [
          { required: true, message: "订单备注不能为空", trigger: "change" },
        ],
      },

      showIndex: 0,
    };
  },
  computed: {
    overflowAmount() {
      return (
        Number(this.form1.actAmount || 0) - Number(this.form1.amount || 0)
      ).toFixed(2);
    },
  },
  created() {
    if (localStorage.getItem("queryParams")) {
      this.queryParams = JSON.parse(localStorage.getItem("queryParams"));
      this.getList();
    }
  },
  methods: {
    checkNumber(rule, value, callback) {
      const reg = /^(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/; // 正数且小数点后最多两位的正则表达式
      if (value === '' || reg.test(value)) {
        callback();
      } else {
        callback(new Error('请输入正数且小数点后最多两位的数字'));
      }
    },
    checkPhone(rule, value, callback) {
      const reg = /^1[0-9]{10}$/; // 手机号正则表达式
      if (value === "" || reg.test(value)) {
        callback();
      } else {
        callback(new Error("请输入有效的手机号码"));
      }
    },
    checkInteger(rule, value, callback) {
      const reg = /^[1-9]\d*$/;
      if (value === "" || reg.test(value)) {
        callback();
      } else {
        callback(new Error("请输入正整数"));
      }
    },
    validateInput() {
      this.form1.actAmount = this.form1.actAmount.replace(/[^0-9.]/g, ""); // 只保留数字和小数点

      const parts = this.form1.actAmount.split(".");
      if (parts.length > 2) {
        const integerPart = parts[0];
        const decimalPart = parts.slice(1).join("");
        this.form1.actAmount = `${integerPart}.${decimalPart}`;
      } else if (parts.length === 2 && parts[1].length > 2) {
        this.form1.actAmount = `${parts[0]}.${parts[1].slice(0, 2)}`; // 控制小数位数最多为两位
      }
    },
    handleKeyDown(event) {
      // 允许用户删除小数点后再次输入小数点
      if (event.key === "." && this.form1.actAmount.includes(".")) {
        event.preventDefault();
        return;
      }

      // 处理回退键删除小数点后再次输入小数点
      if (event.key === "Backspace") {
        const lastChar = this.form1.actAmount.slice(-1);
        if (lastChar === ".") {
          this.form1.actAmount = this.form1.actAmount.slice(0, -1); // 删除最后一个字符（小数点）
        }
      }
    },

    // 搜索
    handleQuery() {
      this.getList();
    },

    // 获取列表
    getList() {
      if (this.queryParams.typeText === "") {
        this.$message.warning("请输入搜索内容");
        return;
      }

      this.loading = true;
      let params = {
        mobile:
          this.queryParams.type === "mobile"
            ? this.queryParams.typeText
            : undefined,
        orderId:
          this.queryParams.type === "orderId"
            ? this.queryParams.typeText
            : undefined,
        certNo:
          this.queryParams.type === "certNo"
            ? this.queryParams.typeText
            : undefined,
      };

      queryRepayPlan(params)
        .then((res) => {
          this.detail = res.data;
          this.phone = res.data.mobile;
          // 本地保存查询条件
          localStorage.setItem("queryParams", JSON.stringify(this.queryParams));
        })
        .finally(() => {
          this.loading = false;
        });
    },

    changeShow(index) {
      this.showIndex = index;
    },

    // 结清试算
    handleTrial() {
      this.changeShow(1);

      let params = {
        repayPurpose: "CLEAR",
        orderId: this.detail.orderId,
      };

      trial(params).then((res) => {
        this.trialRes = [res.data];
      });
    },

    // 申请减免
    handleReduceApply() {
      this.restForm();
      this.changeShow(2);

      let params = {
        repayPurpose: "CURRENT",
        orderId: this.detail.orderId,
      };

      trial(params).then((res) => {
        this.currentPeriod = res.data.period;
        this.currentAmount = res.data.amount;
      });
    },

    // 切换期数
    change1(e) {
      this.currentPeriod = null;
      this.currentAmount = null;

      let params = {
        repayPurpose: e === 1 ? "CURRENT" : "CLEAR",
        orderId: this.detail.orderId,
      };

      trial(params).then((res) => {
        this.currentPeriod = res.data.period;
        this.currentAmount = res.data.amount;
      });
    },

    // 提交申请减免
    onSubmit() {
      if (!this.form.type) {
        this.$message.error("请选择减免期数");
        return;
      }

      if (Number(this.form.reduceAmount) > Number(this.currentAmount)) {
        this.$message.error("减免金额不能大于计划金额");
        return;
      }

      let params = {
        repayPurpose: this.form.type === 2 ? "CLEAR" : "CURRENT", //	是	还款方式CLEAR 结清 CURRENT 当期
        period: this.form.type === 2 ? undefined : this.currentPeriod, //当期必填
        orderId: this.detail.orderId, //	订单id
        reduceAmount: this.form.reduceAmount, // 减免金额
        remark: this.form.remark,
      };
      reduceApply(params).then((res) => {
        this.$message.success("操作成功！");

        this.showIndex = 0;
        this.restForm();
      });
    },

    // 重置申请减免表单
    restForm() {
      this.form = {
        type: 1,
        reduceAmount: 0,
        remark: "",
      };
    },

    // 销账预览
    handleRepayApplyPreview() {
      this.restForm1();
      this.changeShow(3);

      let params = {
        repayPurpose: "CURRENT",
        orderId: this.detail.orderId,
      };

      repayApplyPreview(params).then((res) => {
        this.currentPeriod = res.data.period;
        this.form1.period = res.data.period;
        this.form1.amount = res.data.amount;
        this.form1.reduceAmount = res.data.reduceAmount;
        this.form1.actAmount = res.data.actAmount;

        // 存在减免金额时 实还金额输入框置灰
        if (res.data.reduceAmount > 0) {
          this.actAmountDisabled = true;
        } else if (res.data.reduceAmount === 0) {
          this.actAmountDisabled = false;
        }
      });
    },

    // 重置销账表单
    restForm1() {
      this.form1 = {
        type: 1,
        period: null,
        amount: null, // 客户应还金额
        reduceAmount: null, // 减免金额
        actAmount: null, //实还金额
      };
    },

    // 切换期数
    change2(e) {
      console.log(e);
      this.currentPeriod = null;
      this.form1.period = null;
      this.form1.amount = null;
      this.form1.reduceAmount = null;
      this.form1.actAmount = null;

      let params = {
        repayPurpose: e === 1 ? "CURRENT" : "CLEAR",
        orderId: this.detail.orderId,
      };
      repayApplyPreview(params).then((res) => {
        this.currentPeriod = res.data.period;
        this.form1.period = res.data.period;
        this.form1.amount = res.data.amount;
        this.form1.reduceAmount = res.data.reduceAmount;
        this.form1.actAmount = res.data.actAmount;

        // 存在减免金额时 实还金额输入框置灰
        if (res.data.reduceAmount > 0) {
          this.actAmountDisabled = true;
        } else if (res.data.reduceAmount === 0) {
          this.actAmountDisabled = false;
        }
      });
    },

    handleSearchInput(value) {
      deBounce(() => {
        this.calSpilloverAmt(value);
      }, 300)();
    },

    // 溢出金额 = 实还金额 - 应还金额
    calSpilloverAmt(value) {
      this.form1.spilloverAmt = math
        .subtract(
          math.bignumber(value === "" ? 0 : value),
          math.bignumber(this.form1.payableAmt)
        )
        .toNumber();
    },

    // 销账提交
    onSubmit1() {
      if (!this.form1.type) {
        this.$message.error("请选择还款期数");
        return;
      }

      if (
        !this.actAmountDisabled &&
        Number(this.form1.actAmount) < Number(this.form1.amount)
      ) {
        this.$message.error("实还金额应大于或等于客户应还金额");
        return;
      }

      this.$confirm("是否确认销账提交?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        roundButton: true,
        type: "warning",
      }).then(() => {
        let params = {
          repayPurpose: this.form1.type === 2 ? "CLEAR" : "CURRENT", //	是	还款方式CLEAR 结清 CURRENT 当期
          period: this.form1.type === 2 ? undefined : this.currentPeriod, //当期必填
          orderId: this.detail.orderId, //	订单id
          amount: this.form1.amount,
          reduceAmount: this.form1.reduceAmount,
          actAmount: this.form1.actAmount,
          overflowAmount:
            Number(this.form1.reduceAmount) === 0
              ? Number(this.overflowAmount)
              : undefined,
        };

        repayApply(params).then((res) => {
          this.$message.success("操作成功！");

          this.showIndex = 0;
        });
      });
    },

    // 关闭备注弹窗
    closeRemark() {
      this.remarkDialogVisible = false;
      this.ruleFormRemark = {};
      this.$refs["ruleFormRemark"].resetFields();
    },

    // 聚合支付
    handleAggregatePay(mode) {
      this.mode = mode;
      this.changeShow(5);
      this.currentPeriod = null;
      if (mode === "H5") {
        this.form2 = {
          payChannel: "YIBEIJIA_SCAN",
          type: 1,
          period: null,
          amount: null,
          reduceAmount: null,
          actAmount: null,
        };
      } else {
        this.form2 = {
          type: 1,
          period: null,
          amount: null,
          reduceAmount: null,
          actAmount: null,
        };
      }

      this.getAggregatePayInfo();
    },

    getAggregatePayInfo() {
      let params = {
        repayPurpose: this.form2.type === 1 ? "CURRENT" : "CLEAR",
        orderId: this.detail.orderId,
      };

      repayApplyPreview(params).then((res) => {
        this.currentPeriod = res.data.period;
        this.form2.period = res.data.period;
        this.form2.amount = res.data.amount;
        this.form2.reduceAmount = res.data.reduceAmount;
        this.form2.actAmount = res.data.actAmount;
      });
    },

    change3(e) {
      this.getAggregatePayInfo();
    },

    onSubmitForm2() {
      let params = {
        repayPurpose: this.form2.type === 1 ? "CURRENT" : "CLEAR",
        orderId: this.detail.orderId,
        period: this.form2.period,
        amount: this.form2.amount,
        reduceAmount: this.form2.reduceAmount,
        actAmount: this.form2.actAmount,
        payChannel: "YIBEIJIA",
        productChannel: "QHYP",
      };
      aggregatePayApply(params).then((res) => {
        this.$message.success("操作成功");
      });
    },

    onSubmitForm2H5() {
      this.$refs["form2"].validate((valid) => {
        if (valid) {
          let params = {
            repayPurpose: this.form2.type === 1 ? "CURRENT" : "CLEAR",
            orderId: this.detail.orderId,
            period: this.form2.period,
            amount: this.form2.amount,
            reduceAmount: this.form2.reduceAmount,
            actAmount: this.form2.actAmount,
            // payChannel: 'ZHONGJIN',
            payChannel: this.form2.payChannel,
            productChannel: "QHYP",
            payerAcctCode: this.form2.payerAcctCode,
            payerUserName: this.form2.payerUserName,
          };

          aggregatePayApply(params).then((res) => {
            if (this.form2.payChannel === 'YIBEIJIA_SCAN') {
              this.codeRes = res.data;
              this.dialogCodeVisible = true;
            } else if (this.form2.payChannel === 'BAOFOO_TRANS_PAY') {
              this.$message.success('操作成功')
            }
          });
        }
      });
    },

    closeCodeDialog() {
      this.dialogCodeVisible = false;
    },

    openSend() {
      this.dialogSendVisible = true;
    },
    closeSendDialog() {
      this.dialogSendVisible = false;
    },

    // 发送
    send() {
      if (!this.phone) {
        this.$message.error("请输入手机号码");
        return;
      }

      let params = {
        mobile: this.phone,
        aggregatePayId: this.codeRes.aggregatePayId,
      };

      sendPayUrlSms(params).then((res) => {
        this.$message.success("发送成功");
        this.closeSendDialog();
      });
    },
  },
};
</script>
