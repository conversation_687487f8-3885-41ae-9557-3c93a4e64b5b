<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="订单号">
        <el-input placeholder="请输入" clearable v-model="queryParams.orderId" style="width:200px;" />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input placeholder="请输入" clearable v-model="queryParams.mobile" style="width:200px;" />
      </el-form-item>
      <el-form-item label="银行卡号">
        <el-input placeholder="请输入" clearable v-model="queryParams.payerAcctCode" style="width:200px;" />
      </el-form-item>
      <el-form-item label="支付结果">
        <el-select v-model="queryParams.payResult" placeholder="请选择" style="width: 160px;" clearable>
          <el-option label="成功" value="SUCCESS"></el-option>
          <el-option label="失败" value="FAILED"></el-option>
          <el-option label="待支付" value="WAITING"></el-option>
          <el-option label="处理中" value="PROCESSING"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发起时间">
        <el-date-picker v-model="dataVal" size="small" type="datetimerange" style="width: 380px"
          value-format="yyyy-MM-dd HH:mm:ss" range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button round type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button round type="success" size="mini" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table border="border" :data="list" v-loading="loading">
      <el-table-column label="聚合订单号" prop="id" align="center"></el-table-column>
      <el-table-column label="订单编号" prop="orderId" align="center"></el-table-column>
      <el-table-column label="支付流水号" prop="merchantOutOrderNo" align="center"></el-table-column>
      <el-table-column label="支付回单流水号" prop="outerOrderNo" align="center"></el-table-column>
      <el-table-column label="交易流水号" prop="outerTradeNo" align="center"></el-table-column>
      <el-table-column label="姓名" prop="name" align="center"></el-table-column>
      <el-table-column label="手机号" prop="mobile" align="center"></el-table-column>
      <el-table-column label="银行卡号" prop="payerAcctCode" align="center"></el-table-column>
      <el-table-column label="订单状态" prop="applyState" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.applyState === 'NORMAL'">正常</span>
          <span v-if="scope.row.applyState === 'EXPIRED'">过期</span>
          <span v-if="scope.row.applyState === 'SUCCESS'">成功</span>
          <span v-if="scope.row.applyState === 'FAILED'">失败</span>
        </template>
      </el-table-column>
      <el-table-column label="发起时间" prop="applyTime" align="center"></el-table-column>
      <el-table-column label="支付时间" prop="payTime" align="center"></el-table-column>
      <el-table-column label="支付结果" prop="payResult" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.payResult === 'SUCCESS'">成功</span>
          <span v-if="scope.row.payResult === 'FAILED'">失败</span>
          <span v-if="scope.row.payResult === 'PROCESSING'">处理中</span>
          <span v-if="scope.row.payResult === 'WAITING'">待支付</span>
        </template>
      </el-table-column>
      <el-table-column label="还款期数" prop="period" align="center"></el-table-column>
      <el-table-column label="应还金额" prop="amount" align="center"></el-table-column>
      <el-table-column label="减免金额" prop="reduceAmount" align="center"></el-table-column>
      <el-table-column label="减免后金额" prop="actAmount" align="center"></el-table-column>
      <el-table-column label="支付通道" prop="payChannel" align="center"></el-table-column>
      <el-table-column label="支付链接" prop="payUrl" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleOpenPayUrl(scope.row.payUrl)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="销账状态" prop="writeOffStatus" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.writeOffStatus === 'Y'">已销账</span>
          <span v-if="scope.row.writeOffStatus === 'N'">未销账</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 支付链接弹窗 -->
    <el-dialog title="支付链接" :visible.sync="dialogVisible" width="500px">
      <div style="font-size: 14px; margin-bottom: 10px;">
        {{ payUrl }}
      </div>
      <div style="display: flex; justify-content: center;">
        <el-button round type="primary" size="mini" @click="copy()">复制</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  queryAggregatePayPage,
  downloadExcel,
} from "@/api/postLoanManage";
import fileDownload from 'js-file-download'

export default {
  name: "",
  data() {
    return {
      // 查询参数
      queryParams: {
        orderId: undefined,
        mobile: undefined,
        payerAcctCode:undefined,
        payResult: undefined,
        pageNum: 1,
        pageSize: 10,
        productChannels:["QHYP"],
      },
      loading: false,
      list: [],
      total: 0,
      dataVal: [],
      dialogVisible: false,
      payUrl: '',

      pickerOptionsDefault: {
        onPick: ({
          maxDate,
          minDate
        }) => {
          //当我们选择两个值的时候，就认为用户已经选择完毕
          if (maxDate != null && minDate != null) {
            this.repayDateBegin = maxDate;
            this.repayDateEnd = minDate;
          }
        },
        disabledDate: time => {
          let maxDate = this.maxDate;
          let minDate = this.minDate;
          if (maxDate != null && minDate != null) {
            let days = maxDate.getTime() - minDate.getTime();
            //计算完之后必须清除，否则选择器一直处于禁止选择的状态
            this.maxDate = null;
            this.minDate = null;
            return parseInt(days / (1000 * 60 * 60 * 24)) > 30;
          } else {
            //设置当前时间后的时间不可选
            return time.getTime() > Date.now();
          }
        }
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 获取列表
    getList() {
      this.loading = true;

      let params = {};

      if (this.dataVal && this.dataVal.length === 2) {
        params.startTime = this.dataVal[0];
        params.endTime = this.dataVal[1];
      } else {
        params.startTime = undefined;
        params.endTime = undefined;
      }

      params = {
        ...params,
        ...this.queryParams
      };

      queryAggregatePayPage(params).then(res => {
        this.list = res.data.list;
        this.total = res.data.total;
        this.loading = false;
      });
    },

    // 查看支付链接
    handleOpenPayUrl(payUrl) {
      this.payUrl = payUrl
      this.dialogVisible = true
    },

    // 复制
    copy() {
      this.copyTextToClipboard(this.payUrl)
    },

    async copyTextToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text);
        this.$message.success('复制成功')
      } catch (err) {
        console.error('Failed to copy: ', err);
      }
    },

    // 导出
    handleExport() {
      this.$confirm('是否确认导出所有数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        roundButton: true,
        type: "warning"
      }).then(() => {
        let params = {...this.queryParams};

        if (this.dataVal && this.dataVal.length === 2) {
          params.startTime = this.dataVal[0];
          params.endTime = this.dataVal[1];
        } else {
          params.startTime = undefined;
          params.endTime = undefined;
        }
        downloadExcel(params).then(res => {
          fileDownload(res, '聚合支付记录.xlsx');
        })
      })
    }
  }
};
</script>
