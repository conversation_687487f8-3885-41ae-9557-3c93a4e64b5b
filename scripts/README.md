# Git Hooks 配置指南

## 概述

本项目提供了 Git pre-commit hook，用于在每次提交前自动执行 Maven validate 命令，包括 checkstyle 检查。这确保了代码质量和风格的一致性。

## 快速开始

### 安装 Git Hooks

**所有团队成员在克隆项目后都需要执行以下步骤：**

#### Windows 用户：
```bash
# 在项目根目录下执行
scripts\install-hooks.bat

# 或者在scripts目录下执行
cd scripts
install-hooks.bat
```

#### Linux/Mac 用户：
```bash
# 在项目根目录下执行
chmod +x scripts/install-hooks.sh
./scripts/install-hooks.sh

# 或者在scripts目录下执行
cd scripts
chmod +x install-hooks.sh
./install-hooks.sh
```

### 验证安装

安装完成后，尝试提交来测试 hook 是否正常工作：
```bash
git add .
git commit -m "测试提交"
```

如果看到 "Running Maven validate..." 消息，说明 hook 已正确安装。

查看方式：IDEA——Git——Console

## Hook 详细信息

### Pre-commit Hook

**功能**:
- 在每次 `git commit` 执行前自动运行 `mvn validate`
- 包含 checkstyle 检查（已在 pom.xml 中配置）
- 如果验证失败，提交将被阻止

### Hook 工作原理

1. **执行验证**: 运行 `mvn validate` 命令
2. **处理结果**:
   - 如果成功，允许提交继续
   - 如果失败，阻止提交并显示错误信息

## 项目文件结构

```
scripts/
├── hooks/
│   ├── pre-commit          # Shell 脚本版本的 hook
├── install-hooks.sh        # Linux/Mac 安装脚本
├── install-hooks.bat       # Windows 安装脚本
└── README.md              # 本文档
```

这些文件都被 Git 管理，确保所有团队成员都能获得相同的 hook 配置。

## Maven Checkstyle 配置

项目中的 checkstyle 配置位于 `pom.xml` 中：

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-checkstyle-plugin</artifactId>
    <version>3.2.1</version>
    <configuration>
        <consoleOutput>true</consoleOutput>
        <failsOnError>true</failsOnError>
        <linkXRef>false</linkXRef>
        <configLocation>checkstyle.xml</configLocation>
    </configuration>
    <executions>
        <execution>
            <id>validate</id>
            <phase>validate</phase>
            <goals>
                <goal>check</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

## 使用说明

### 正常提交流程

```bash
git add .
git commit -m "你的提交信息"
```

如果代码通过所有检查，提交将正常进行。

### 处理验证失败

如果 hook 检测到问题，你会看到类似的错误信息：

```
❌ Maven validate failed! Please fix the issues before committing.
   Common issues:
   - Checkstyle violations
   - Compilation errors
   - Other validation failures

To fix checkstyle issues, you can run:
   mvn checkstyle:check

To see detailed checkstyle report:
   mvn checkstyle:checkstyle
```

### 修复 Checkstyle 问题

1. **查看详细报告**:
   ```bash
   mvn checkstyle:checkstyle
   ```

2. **只检查 checkstyle**:
   ```bash
   mvn checkstyle:check
   ```

3. **修复代码风格问题后重新提交**:
   ```bash
   git add .
   git commit -m "修复代码风格问题"
   ```

### 临时跳过 Hook（强烈不推荐）

在紧急情况下，可以跳过 hook：

```bash
git commit --no-verify -m "紧急提交"
```

**注意**: 这会跳过所有验证，应该谨慎使用。

## 故障排除

### Maven 未找到

如果看到 "Maven not found" 错误：

1. **检查环境变量是否配置正确**:
   - 确保将 Maven 的 `bin` 目录添加到系统 PATH
   - 如果是 IDEA 自带的Maven，请检查该路径：`C:\Program Files\JetBrains\{IntelliJ IDEA 版本号}\plugins\maven`

2. **重启IDEA**

3. **验证 Maven 安装**:
   ```bash
   mvn --version
   ```

### Hook 不执行

1. **检查文件权限**: 确保 `.git/hooks/pre-commit` 文件存在
2. **检查 Git 配置**: 确保没有禁用 hooks
3. **手动测试**: 直接运行 `mvn validate` 测试

## 团队协作

### 新成员加入项目

1. **克隆项目**：
   ```bash
   git clone <项目地址>
   cd <项目目录>
   ```

2. **安装 hooks**：
   ```bash
   # Windows
   scripts\install-hooks.bat

   # Linux/Mac
   ./scripts/install-hooks.sh
   ```

3. **验证安装**：
   ```bash
   git add .
   git commit -m "测试提交"
   ```

### 更新 Hooks

当 hooks 脚本更新时，团队成员需要重新运行安装脚本：

```bash
# 拉取最新代码
git pull

# 重新安装 hooks
scripts\install-hooks.bat  # Windows
# 或
./scripts/install-hooks.sh  # Linux/Mac
```

### 确保团队一致性

1. 所有开发者都安装了 Maven
2. 统一使用相同的 checkstyle 配置
3. 定期更新 hook 脚本（通过 Git 同步）
4. 新成员入职时提醒安装 hooks

## 最佳实践

1. **提交前本地验证**: 在提交前手动运行 `mvn validate`
2. **小步提交**: 避免大量更改的单次提交
3. **及时修复**: 不要积累太多代码风格问题
4. **团队沟通**: 如需修改 checkstyle 规则，与团队讨论
