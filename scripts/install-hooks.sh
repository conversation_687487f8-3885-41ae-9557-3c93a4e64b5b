#!/bin/bash
#
# Install Git hooks for the project
# This script copies the hooks from scripts/hooks/ to .git/hooks/
#

set -e

# Get the repository root directory
REPO_ROOT=$(git rev-parse --show-toplevel)
HOOKS_DIR="$REPO_ROOT/.git/hooks"
SOURCE_HOOKS_DIR="$REPO_ROOT/scripts/hooks"

echo "Installing Git hooks..."

# Check if source hooks directory exists
if [ ! -d "$SOURCE_HOOKS_DIR" ]; then
    echo "❌ Error: Source hooks directory not found: $SOURCE_HOOKS_DIR"
    exit 1
fi

# Check if .git/hooks directory exists
if [ ! -d "$HOOKS_DIR" ]; then
    echo "❌ Error: Git hooks directory not found: $HOOKS_DIR"
    echo "   Make sure you're in a Git repository."
    exit 1
fi

# Install pre-commit hook
if [ -f "$SOURCE_HOOKS_DIR/pre-commit" ]; then
    echo "Installing pre-commit hook..."
    cp "$SOURCE_HOOKS_DIR/pre-commit" "$HOOKS_DIR/pre-commit"
    chmod +x "$HOOKS_DIR/pre-commit"
    echo "✅ pre-commit hook installed"
else
    echo "⚠️  Warning: pre-commit hook not found in source directory"
fi



echo ""
echo "🎉 Git hooks installation completed!"
echo ""
echo "The following hooks are now active:"
echo "  - pre-commit: Runs 'mvn validate' before each commit"
echo ""
echo "To test the hook, try making a commit:"
echo "  git add ."
echo "  git commit -m 'Test commit'"
echo ""
echo "To temporarily skip hooks (not recommended):"
echo "  git commit --no-verify -m 'Skip hooks'"
echo ""
