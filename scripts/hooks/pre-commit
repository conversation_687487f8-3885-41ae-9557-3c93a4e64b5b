#!/bin/sh
#
# Git pre-commit hook to run <PERSON><PERSON> validate (including checkstyle)
# This hook will run 'mvn validate' before each commit
# If validation fails, the commit will be aborted
#

echo ""
echo "========================================"
echo "    PRE-COMMIT HOOK ACTIVE"
echo "========================================"
echo ""

# Change to the repository root directory
REPO_ROOT=$(git rev-parse --show-toplevel)
cd "$REPO_ROOT"

echo "Running Maven validate..."
echo ""

# Check if mvn command exists
if ! command -v mvn >/dev/null 2>&1; then
    echo "*** ERROR: Maven not found ***"
    echo "Please install Maven and add it to your PATH"
    echo ""
    exit 1
fi

# Run Maven validate
mvn validate

# Check the exit code
if [ $? -ne 0 ]; then
    echo ""
    echo "*** COMMIT BLOCKED ***"
    echo "Maven validate failed! Please fix the issues above."
    echo ""
    echo "Common fixes:"
    echo "- Fix checkstyle violations"
    echo "- Resolve compilation errors"
    echo "- Check test failures"
    echo ""
    echo "To skip validation temporarily (not recommended):"
    echo "  git commit --no-verify -m \"your message\""
    echo ""
    exit 1
fi

echo ""
echo "✅ Maven validate passed!"
echo "========================================"
echo "    PRE-COMMIT HOOK COMPLETED"
echo "========================================"
echo ""
exit 0
