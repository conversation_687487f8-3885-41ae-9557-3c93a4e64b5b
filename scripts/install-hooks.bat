@echo off
echo Installing Git hooks...

REM Get the actual repository root using git command
for /f "tokens=*" %%i in ('git rev-parse --show-toplevel 2^>nul') do set REPO_ROOT_UNIX=%%i
if "%REPO_ROOT_UNIX%"=="" (
    echo Error: Not in a Git repository or Git not found
    pause
    exit /b 1
)

REM Convert Unix path to Windows path
set REPO_ROOT=%REPO_ROOT_UNIX:/=\%
set HOOKS_DIR=%REPO_ROOT%\.git\hooks
set SOURCE_HOOKS_DIR=%REPO_ROOT%\scripts\hooks

echo Repository root: %REPO_ROOT%
echo Hooks directory: %HOOKS_DIR%
echo Source hooks directory: %SOURCE_HOOKS_DIR%

REM Check if source hooks directory exists
if not exist "%SOURCE_HOOKS_DIR%" (
    echo Error: Source hooks directory not found: %SOURCE_HOOKS_DIR%
    pause
    exit /b 1
)

REM Check if .git/hooks directory exists
if not exist "%HOOKS_DIR%" (
    echo Error: Git hooks directory not found: %HOOKS_DIR%
    echo Make sure you are in a Git repository.
    pause
    exit /b 1
)

REM Install pre-commit hook
if exist "%SOURCE_HOOKS_DIR%\pre-commit" (
    echo Installing pre-commit hook...
    copy "%SOURCE_HOOKS_DIR%\pre-commit" "%HOOKS_DIR%\pre-commit" >nul
    echo pre-commit hook installed successfully
) else (
    echo Warning: pre-commit hook not found in source directory
)



echo.
echo Git hooks installation completed!
echo.
echo The following hooks are now active:
echo   - pre-commit: Runs 'mvn validate' before each commit
echo.
echo To test the hook, try making a commit:
echo   git add .
echo   git commit -m "Test commit"
echo.
echo To temporarily skip hooks (not recommended):
echo   git commit --no-verify -m "Skip hooks"
echo.
pause
